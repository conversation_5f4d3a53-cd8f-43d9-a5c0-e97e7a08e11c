<template>
  <bf-dialog
    v-model="visible"
    :title="$t('header.setting')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    center
    class="setting-dialog"
    modal-class="dialog-modal-mask"
    :width="calcScaleSize(592)"
    @open="onOpen"
  >
    <bf-tabs-container ref="bfTabsContainerRef">
      <el-tabs v-model="activeTabName" @tab-change="updateDimensions">
        <el-tab-pane name="userSetting" :label="$t('header.userSet')" class="flex flex-wrap justify-between personal-setting-tab-pane">
          <div v-for="(item, index) in settings" :key="index" class="basis-[50%] personal-setting-item">
            <bf-checkbox v-model="userSetting[item.key]" :label="item.label" :disabled="item.disabled" @change="confirm_userSettings(item)" />
          </div>
        </el-tab-pane>
        <el-tab-pane name="sysSetting" :label="$t('header.sysSet')" class="system-setting-tab-pane">
          <el-form ref="sysSetRef" :model="sysSet" :label-width="accountFormLabelWidth" label-position="left" class="bf-form">
            <el-form-item :label="$t('header.logo')" class="upload-logo">
              <div class="sys_logo_img">
                <img :src="sysSet.clientLogo || defaultLogoPath" />
              </div>
              <div class="sys_title_prompt">
                <span>{{ $t('dialog.selectLogoPrefix') }}</span>
                <span class="text-[var(--bf-text-highlight)]">{{ $t('dialog.selectLogoSize') }}</span>
                <span>{{ $t('dialog.selectLogoSuffix') }}</span>
              </div>
              <div class="w-full flex justify-center items-start gap-4">
                <el-upload class="upload-input" action="" :auto-upload="false" :multiple="false" :on-change="handleFileChange" :show-file-list="false">
                  <bf-button color-type="warning">{{ $t('dialog.selectImage') }}</bf-button>
                </el-upload>
                <bf-button v-if="false" color-type="info" @click="resetToDefault">恢复默认</bf-button>
              </div>
            </el-form-item>
            <el-form-item :label="$t('header.moveText')" prop="clientTitle">
              <bf-input v-model="sysSet.clientTitle" :maxlength="128" />
            </el-form-item>
            <el-form-item label="" prop="clientSubTitle">
              <bf-input v-model="sysSet.clientSubTitle" :maxlength="128" />
            </el-form-item>
            <el-form-item>
              <div class="w-full flex justify-center">
                <bf-button color-type="primary" @click="save_sys_logo_and_title">{{ $t('dialog.confirm') }}</bf-button>
              </div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane name="accountManager" :label="$t('header.accountManager')" class="account-setting-tab-pane">
          <el-form ref="accountSet" :model="account" :label-width="accountFormLabelWidth" :rules="accountRule" label-position="left" class="bf-form">
            <el-form-item :label="$t('header.oldPwd')" prop="oldPwd">
              <bf-input v-model="account.oldPwd" type="password" show-password @keyup.enter="resetPassword" />
            </el-form-item>
            <el-form-item :label="$t('header.newPwd')" prop="newPwd">
              <bf-input v-model="account.newPwd" type="password" show-password @keyup.enter="resetPassword" />
            </el-form-item>
            <el-form-item :label="$t('header.newPwd2')" prop="newPwd2">
              <bf-input v-model="account.newPwd2" type="password" show-password @keyup.enter="resetPassword" />
            </el-form-item>
            <el-form-item>
              <div class="w-full flex justify-center">
                <bf-button color-type="primary" @click="resetPassword">{{ $t('dialog.confirm') }}</bf-button>
              </div>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </bf-tabs-container>
  </bf-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, onBeforeUnmount, watch, customRef } from 'vue'
  import { useI18n } from 'vue-i18n'
  import i18n, { SupportedLang } from '@/modules/i18n'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfproto from '@/modules/protocol/index'
  import bfprocess, { updateGlobalUserSettingEffect } from '@/utils/bfprocess'
  import bfCrypto from '@/utils/crypto'
  import bfNotify, { warningBox } from '@/utils/notify'
  import { cloneDeep } from 'lodash'
  import validateRules from '@/utils/validateRules'
  import bfDialog from '@/components/bfDialog/main'
  import bfCheckbox from '@/components/bfCheckbox/main'
  import bfInput from '@/components/bfInput/main'
  import bfTabsContainer from '@/components/bfTabsContainer.vue'
  import { convertPxToRemWithUnit, calcScaleSize } from '@/utils/setRem'
  import bfButton from '@/components/bfButton'

  interface Props {
    modelValue: boolean
  }

  const props = defineProps<Props>()
  const emit = defineEmits<{
    (e: 'update:modelValue', value: boolean): void
  }>()

  const { t, locale } = useI18n()
  const sysSetRef = ref()
  const accountSetRef = ref()
  const visible = customRef<boolean>((track: () => void, trigger: () => void) => ({
    get() {
      track()
      return props.modelValue
    },
    set(value) {
      emit('update:modelValue', value)
      trigger()
    },
  }))
  const bfTabsContainerRef = ref()
  const activeTabName = ref('userSetting')
  const updateDimensions = () => {
    bfTabsContainerRef.value?.updateDimensions()
  }

  const userSetting = ref(cloneDeep(bfglob.userInfo.setting))

  const sysSet = reactive({
    clientLogo: bfglob.systemSetting.clientLogo,
    clientTitle: bfglob.systemSetting.clientTitle,
    clientSubTitle: bfglob.systemSetting.clientSubTitle,
  })

  const account = reactive({
    oldPwd: '',
    newPwd: '',
    newPwd2: '',
  })

  // const defaultLogo = `/logo.${bfglob.siteConfig?.logoExt || 'jpg'}`
  const defaultLogoPath = new URL('@/assets/images/loginBg/1x/logo.webp', import.meta.url).href

  const onOpen = () => {
    Object.assign(sysSet, {
      clientLogo: bfglob.systemSetting.clientLogo,
      clientTitle: bfglob.systemSetting.clientTitle,
      clientSubTitle: bfglob.systemSetting.clientSubTitle,
    })
  }

  const handleFileChange = uploadFile => {
    const file = uploadFile.raw

    // 验证文件大小：不能大于1M
    if (file.size >= 1024 * 1024) {
      warningBox(t('msgbox.logoSizeError'), 'error')
      return
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
    if (!allowedTypes.includes(file.type)) {
      warningBox(t('msgbox.logoTypeError'), 'error')
      return
    }

    // 创建图片对象来验证尺寸
    const img = new Image()
    const reader = new FileReader()

    reader.onload = function (e) {
      img.onload = function () {
        // 验证图片尺寸：不大于500*164
        if (img.width > 500 || img.height > 164) {
          warningBox(t('msgbox.logoDimensionError'), 'error')
          return
        }

        // 验证通过，设置图片数据
        sysSet.clientLogo = e.target.result
      }

      img.onerror = function () {
        warningBox(t('msgbox.logoLoadError'), 'error')
      }

      img.src = e.target.result as string
    }

    reader.onerror = function () {
      warningBox(t('msgbox.logoReadError'), 'error')
    }

    reader.readAsDataURL(file)
  }

  const resetToDefault = () => {
    sysSet.clientLogo = ''
    // 规则验证成功，可以提交数据
    if (bfglob.systemSetting.logoRid === '') {
      // 添加LOGO
      bfprocess.saveSysLogoAndTitle(sysSet.clientLogo, dbCmd.DB_SYS_CONFIG_INSERT, 'clientLogo')
    } else {
      // 更新LOGO
      bfprocess.saveSysLogoAndTitle(sysSet.clientLogo, dbCmd.DB_SYS_CONFIG_UPDATE, 'clientLogo', bfglob.systemSetting.logoRid)
    }
  }

  const confirm_userSettings = settingItem => {
    userSetting.value.ispUdateVoipSpeakInfo = false
    const oldSettings = cloneDeep(bfglob.userInfo.setting)
    bfprocess
      .updateUserSetting(JSON.stringify(userSetting.value), dbCmd.DB_USER_PUPDATE)
      .then(() => {
        bfglob.userInfo.setting = Object.assign({}, bfglob.userInfo.setting, userSetting.value)
        updateGlobalUserSettingEffect(bfglob.userInfo.setting, oldSettings)
        bfglob.emit('update_user_settings', bfglob.userInfo.setting, oldSettings)
      })
      .catch(() => {
        // 恢复选择前状态
        userSetting.value[settingItem.key] = !userSetting.value[settingItem.key]
      })
  }

  const save_sys_logo_and_title = () => {
    sysSetRef.value.validate(function (valid) {
      if (valid) {
        // 规则验证成功，可以提交数据
        if (bfglob.systemSetting.logoRid === '') {
          // 添加LOGO
          bfprocess.saveSysLogoAndTitle(sysSet.clientLogo, dbCmd.DB_SYS_CONFIG_INSERT, 'clientLogo')
        } else {
          // 更新LOGO
          bfprocess.saveSysLogoAndTitle(sysSet.clientLogo, dbCmd.DB_SYS_CONFIG_UPDATE, 'clientLogo', bfglob.systemSetting.logoRid)
        }
        // 去掉滚动标题的换行符
        sysSet.clientTitle = sysSet.clientTitle.trim()
        sysSet.clientSubTitle = sysSet.clientSubTitle.trim()
        if (bfglob.systemSetting.titleRid === '') {
          // 添加标题
          bfprocess.saveSysLogoAndTitle(sysSet.clientTitle, dbCmd.DB_SYS_CONFIG_INSERT, bfglob.systemSetting.titleConfKey)
        } else {
          // 更新标题
          bfprocess.saveSysLogoAndTitle(sysSet.clientTitle, dbCmd.DB_SYS_CONFIG_UPDATE, bfglob.systemSetting.titleConfKey, bfglob.systemSetting.titleRid)
        }

        if (bfglob.systemSetting.subTitleRid === '') {
          // 添加副标题
          bfprocess.saveSysLogoAndTitle(sysSet.clientSubTitle, dbCmd.DB_SYS_CONFIG_INSERT, bfglob.systemSetting.subTitleConfKey)
        } else {
          // 更新副标题
          bfprocess.saveSysLogoAndTitle(
            sysSet.clientSubTitle,
            dbCmd.DB_SYS_CONFIG_UPDATE,
            bfglob.systemSetting.subTitleConfKey,
            bfglob.systemSetting.subTitleRid
          )
        }

        bfNotify.messageBox(i18n.global.t('msgbox.sysSetSuccess'), 'success')
      }
    })
  }

  const updateUserLoginPass = () => {
    const msgObj = {
      orgId: bfglob.userInfo.orgId,
      rid: bfglob.userInfo.rid,
      userLoginPass: bfCrypto.sha256(bfglob.userInfo.origData.userLoginName + account.newPwd),
    }
    const msgOpts = {
      rpcCmdFields: {
        origReqId: 'rid',
        resInfo: 'org_id,rid,user_login_pass',
      },
    }
    bfproto
      .sendMessage(dbCmd.DB_USER_PUPDATE, msgObj, 'db_user', `db.${bfglob.sysId}`, msgOpts)
      .then(rpc_cmd_obj => {
        if (rpc_cmd_obj.resInfo === '+OK') {
          bfNotify.messageBox(t('msgbox.upSuccess'), 'success')
          bfglob.userInfo.origData.userLoginPass = msgObj.userLoginPass
        } else {
          bfNotify.messageBox(t('msgbox.upError'), 'error')
        }
      })
      .catch(err => {
        bfglob.console.warn('updateUserLoginPass timeout', err)
        bfNotify.messageBox(t('msgbox.upError'), 'error')
      })
  }

  const resetPassword = () => {
    accountSetRef.value.validate(valid => {
      if (valid) {
        // 难旧密码是否正确
        var pass_hash = bfCrypto.sha256(bfglob.userInfo.origData.userLoginName + account.oldPwd)
        if (bfglob.userInfo.origData.userLoginPass !== pass_hash) {
          warningBox(t('msgbox.oldPwdError'), 'error')
          return false
        }
        // 更新密码
        updateUserLoginPass()
      } else {
        return false
      }
    })
  }

  const syncUserSettings = userSettingVal => {
    userSetting.value = cloneDeep(userSettingVal)
  }

  onMounted(() => {
    bfglob.on('update_user_settings', syncUserSettings)
  })

  onBeforeUnmount(() => {
    bfglob.off('update_user_settings', syncUserSettings)
  })

  watch(
    () => userSetting.value.showOnlyLocateTerminals,
    val => {
      if (val) {
        userSetting.value.showOnlyOnlineTerminals = true
      }
    }
  )

  const isFR = computed(() => locale.value === SupportedLang.fr)
  const isEN = computed(() => locale.value === SupportedLang.enUS)

  const accountRule = computed(() => {
    return {
      oldPwd: [validateRules.required()],
      newPwd: [validateRules.required()],
      newPwd2: [
        validateRules.required(),
        {
          validator: (rule, value, callback) => {
            if (account.newPwd !== value) {
              callback(new Error(t('msgbox.resetPwdError')))
            } else {
              callback()
            }
          },
          trigger: 'blur',
        },
      ],
    }
  })

  const accountFormLabelWidth = computed(() => {
    if (isFR.value) {
      return `${convertPxToRemWithUnit(calcScaleSize(180))}`
    }
    if (isEN.value) {
      return `${convertPxToRemWithUnit(calcScaleSize(150))}`
    }
    return `${convertPxToRemWithUnit(calcScaleSize(100))}`
  })

  const settings = computed(() => {
    const settingsArr = [
      {
        label: t('header.onAndOffTips'),
        key: 'deviceOnoff',
      },
      {
        label: t('header.startAndEndWorkTips'),
        key: 'changeShifts',
      },
      {
        label: t('header.callingTips'),
        key: 'calling',
      },
      {
        label: t('header.emergencyAlarmTips'),
        key: 'alarmPrompt',
      },
      {
        label: t('header.EGAlarmOnMapCenterTips'),
        key: 'showAlarmOnMapCenter',
      },
      {
        label: t('header.openEGAlarmSound'),
        key: 'alarmVoice',
      },
      {
        label: t('header.InspectionNotice'),
        key: 'showInsNotifi',
      },
      {
        label: t('header.InsOnMapTips'),
        key: 'InsOnMapToolips',
      },
      {
        label: t('header.onMapDisplayLinePointName'),
        key: 'showLinePointName',
      },
      {
        label: t('header.showActivePointBattery'),
        key: 'showActivePointBattery',
      },
      {
        label: t('header.newDevOrCtrlData'),
        key: 'showNewDeviceData',
      },
      {
        label: t('header.showCtrollerStats'),
        key: 'showCtrlOnlineInfo',
      },
      {
        label: t('header.showCtrlMarker'),
        key: 'showCtrlMarker',
      },
      {
        label: t('header.fancytreeSortType'),
        key: 'fancytreeSortType',
      },
      {
        label: t('header.scrollMoveTitle'),
        key: 'scrollMoveTitle',
      },
      {
        label: t('header.showSmsResNotify'),
        key: 'showSmsResNotify',
      },
      {
        label: t('header.showCrossBorderAlarm'),
        key: 'showCrossBorderAlarm',
      },
      {
        label: t('header.showOnlyOnlineTerminals'),
        key: 'showOnlyOnlineTerminals',
        disabled: userSetting.value.showOnlyLocateTerminals,
      },
      {
        label: t('header.showOnlyLocateTerminals'),
        key: 'showOnlyLocateTerminals',
      },
      {
        label: t('header.checkChannelListenGroup'),
        key: 'checkChannelListenGroup',
      },
    ]

    if (bfglob.sysIniConfig.iotEnable) {
      settingsArr.push({
        label: t('header.showIotDeviceMarkers'),
        key: 'showIotDeviceMarkers',
      })
    }

    return settingsArr
  })
</script>

<style lang="scss" scoped>
  .bf-dialog.setting-dialog .el-tabs {
    --el-bg-color-overlay: transparent;
    --el-fill-color-light: transparent;
    --el-color-primary: #fff;
    --el-text-color-primary: #13ecff;
    --el-border-color-light: transparent;

    :deep(.el-tabs__header),
    :deep(.el-tabs__content) {
      background-color: transparent;
      border: none;
    }

    :deep(.el-tabs__header) {
      margin: 0;

      .el-tabs__active-bar,
      .el-tabs__nav-wrap::after {
        display: none;
      }

      .el-tabs__nav .el-tabs__item.is-top {
        padding: 0 20px;
      }
    }

    :deep(.el-tabs__content) {
      // background-color: rgba(6, 121, 204, 0.46);
      // box-shadow: inset 0 0 18px rgba(14, 190, 255, 0.74);
      padding: 18px;

      .system-setting-tab-pane {
        .sys_logo_img {
          border: var(--bf-border-size) solid var(--el-input-border-color, rgba(148, 204, 232, 1));
          width: 100%;
          height: 168px;

          & > img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
</style>
