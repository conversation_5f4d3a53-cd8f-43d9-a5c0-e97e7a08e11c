<template>
  <section
    class="relative w-full h-full overflow-visible tree-grid flex flex-col gap-[5px]"
    :class="[{ 'has-filter': filter }, { 'with-page-header': withPageHeader }]"
  >
    <template v-if="filter">
      <PageHeader v-if="withPageHeader">
        <template #title>
          <treeFilterInput
            class="translate-x-[26px] translate-y-[-12px] you-she-biao-ti-hei"
            ref="filterInput"
            v-model="filterValue"
            @input="filterOnChange"
            @keydown.enter="filterOnChange"
            show-icon
            :is-in-page-header="withPageHeader"
          />
        </template>
      </PageHeader>
      <TreeFilterInput class="h-[35px]" v-else ref="filterInput" v-model="filterValue" @input="filterOnChange" @keydown.enter="filterOnChange" />
    </template>

    <div class="relative w-full tree-grid-container">
      <vxe-table
        ref="tableRef"
        v-bind="$attrs"
        :show-header="false"
        :show-footer="false"
        show-overflow="ellipsis"
        height="100%"
        border="none"
        :keep-source="true"
        :cell-config="vxeCellConfig"
        :row-config="vxeRowConfig"
        :tree-config="props.enableTree ? vxeTreeConfig : null"
        :filter-config="{ isEvery: true }"
        :checkbox-config="vxeCheckConfig"
        :virtual-y-config="{ enabled: true, gt: 0 }"
      >
        <vxe-column
          :type="enableCheckbox ? 'checkbox' : null"
          field="rid"
          :tree-node="props.enableTree"
          :filters="vxeFilterOptions"
          :filter-method="vxeFilterMethod"
        >
          <template v-if="enableCheckbox" #checkbox="{ row, checked, indeterminate }">
            <slot name="checkbox-content" v-bind="{ row, checked, indeterminate }">
              <vxe-column-content
                :enable-checkbox="!props.disabledRids.includes(row.rid) && enableCheckbox"
                :row="row"
                :checked="checked"
                :indeterminate="indeterminate"
                :toggle-checkbox-event="row => toggleCheckboxEvent(row as TreeNodeData, checked)"
                :show-device-channel="showDeviceChannel"
                :show-device-user-name="showDeviceUserName"
              />
            </slot>
          </template>
          <template v-else #default="{ row }">
            <slot name="content" v-bind="{ row }">
              <vxe-column-content :enable-checkbox="enableCheckbox" :row="row" :toggle-checkbox-event="toggleCheckboxEvent" />
            </slot>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
  </section>
</template>

<script lang="ts" setup>
  import type { VxeColumnPropTypes, VxeTableInstance, VxeTablePropTypes } from 'vxe-table'
  import { VxeTable, VxeColumn } from 'vxe-table'
  import { onMounted, reactive, useTemplateRef, ref, computed, onBeforeUnmount } from 'vue'
  import { TreeNodeData, TreeNodeType } from './types'
  import { destroyGlobalVxeTreeManager, DeviceOnlineFilterReg, fullCallDmrIdStr, getFullCallData, getGlobalVxeTreeManager, VxeTreeManager } from './vxeTree'
  import { calcScaleSize } from '@/utils/setRem'
  import { useResizeObserver } from '@vueuse/core'
  import { get_device_status_className as getDeviceStatusClassName } from '@/utils/bftree'

  const emit = defineEmits(['checkbox-change'])

  const props = withDefaults(
    defineProps<{
      filter?: boolean
      withPageHeader?: boolean
      enableCheckbox?: boolean
      enableTree?: boolean
      checkAll?: boolean
      checkStrictly?: boolean
      showDeviceChannel?: boolean
      showDeviceUserName?: boolean
      needDestroyGlobalTree?: boolean
      needFullCallNode?: boolean
      shouldSyncSourceData?: boolean
      filterSourceData?: (node: TreeNodeData) => boolean
      disabledRids?: string[]
    }>(),
    {
      filter: true,
      withPageHeader: false,
      enableCheckbox: true,
      enableTree: true,
      checkAll: true,
      checkStrictly: false,
      showDeviceChannel: true,
      showDeviceUserName: true,
      needDestroyGlobalTree: false,
      needFullCallNode: false,
      shouldSyncSourceData: true,
      filterSourceData: () => true,
      disabledRids: () => [],
    }
  )

  const tableRef = useTemplateRef<VxeTableInstance<TreeNodeData>>('tableRef')

  let treeManager: VxeTreeManager

  const vxeRowConfig = ref<VxeTablePropTypes.RowConfig>({ useKey: true, keyField: 'rid', isHover: true, isCurrent: true })
  const vxeTreeConfig = ref<VxeTablePropTypes.TreeConfig>({
    transform: true,
    rowField: 'rid',
    parentField: 'parentOrgId',
    expandAll: true,
    padding: true,
    reserve: true,
    indent: calcScaleSize(12),
    iconClose: 'bf-iconfont bfdx-shouqi',
    iconOpen: 'bf-iconfont bfdx-zhankai',
    showLine: false,
  })

  const vxeCellConfig = ref<VxeTablePropTypes.CellConfig>({
    height: calcScaleSize(36),
  })

  const vxeCheckConfig = computed<VxeTablePropTypes.CheckboxConfig>(() => ({
    checkAll: props.checkAll,
    checkStrictly: props.checkStrictly,
    reserve: true,
    checkMethod: ({ row }) => {
      return !props.disabledRids.includes(row.rid)
    },
  }))

  useResizeObserver(document.body, () => {
    vxeTreeConfig.value.indent = calcScaleSize(12)
    vxeCellConfig.value.height = calcScaleSize(36)
    tableRef.value?.updateData()
  })

  const toggleCheckboxEvent = (row: TreeNodeData, checked = false) => {
    tableRef.value?.toggleCheckboxRow(row)
    emit('checkbox-change', row, checked)
  }

  /** 过滤树结构--开始 */

  const filterValue = ref('')
  const vxeFilterOptions = reactive<VxeColumnPropTypes.Filters<string>>([{ data: '' }])

  let isFilterOnlineDevice = false
  const vxeFilterMethod: VxeColumnPropTypes.FilterMethod<TreeNodeData> = ({ option, row }) => {
    if (row.nodeType == TreeNodeType.Org) {
      if (isFilterOnlineDevice) {
        return false
      }
      if (row.rid === fullCallDmrIdStr) {
        return getFullCallData().orgShortName.toLowerCase().indexOf(option.data.toLowerCase()) > -1
      }
      const data = bfglob.gorgData.get(row.rid)
      if (data) {
        return data.orgShortName.toLowerCase().indexOf(option.data.toLowerCase()) > -1
      }
    } else {
      const data = bfglob.gdevices.get(row.rid)
      if (data) {
        if (isFilterOnlineDevice) {
          return (
            DeviceOnlineFilterReg.test(getDeviceStatusClassName(bfglob.gdevices.get(row.rid))) &&
            data.selfId.toLowerCase().indexOf(option.data.toLowerCase()) > -1
          )
        }
        return data.selfId.toLowerCase().indexOf(option.data.toLowerCase()) > -1
      }
    }
    return false
  }

  const filterOnChange = (): void => {
    tableRef.value?.setFilter('rid', [{ data: filterValue.value, checked: true }], true)
  }

  /** 过滤树结构--结束 */

  const collapseAll = () => {
    tableRef.value?.clearTreeExpand()
  }

  const expandAll = () => {
    tableRef.value?.setAllTreeExpand(true)
  }

  const displayOnline = () => {
    isFilterOnlineDevice = true
    filterValue.value = ''
    filterOnChange()
  }

  const displayAll = () => {
    isFilterOnlineDevice = false
    filterValue.value = ''
    tableRef.value?.clearFilter('rid')
  }

  // 设置节点选中状态的方法
  const setCheckboxRowByRid = (rid: string | string[], checked: boolean) => {
    const rows = Array.isArray(rid) ? rid.map(id => tableRef.value?.getRowById(id) as TreeNodeData) : (tableRef.value?.getRowById(rid) as TreeNodeData)
    tableRef.value?.setCheckboxRow(rows, checked)
  }

  // 清除所有复选框选中状态
  const clearAllCheckboxRow = () => {
    tableRef.value?.clearCheckboxRow()
  }

  const getTreeManager = () => treeManager

  defineExpose({
    collapseAll,
    expandAll,
    displayOnline,
    displayAll,
    getTreeManager,
    setCheckboxRowByRid,
    clearAllCheckboxRow,
  })

  onMounted(() => {
    treeManager = new VxeTreeManager(tableRef.value, {
      needFullCallNode: props.needFullCallNode,
      filterFunc: props.filterSourceData,
      shouldSyncSourceData: props.shouldSyncSourceData,
    })
    const globalTreeManager = getGlobalVxeTreeManager()
    if (globalTreeManager !== treeManager) {
      treeManager.initTreeData(globalTreeManager.cloneTree() || [])
    }
  })

  onBeforeUnmount(() => {
    if (props.needDestroyGlobalTree) {
      destroyGlobalVxeTreeManager()
    }
  })
</script>

<style lang="scss">
  @use '@/assets/bfdxFont/iconfont.css';
  [data-vxe-ui-theme='dark'] {
    --vxe-ui-font-family: 'AlibabaPuHuiTi2';
    --vxe-ui-font-primary-color: #7e8fa6;
    --vxe-ui-font-color: #fff;
    --vxe-ui-font-lighten-color: #1a7aff;
    --vxe-ui-font-size-default: 12px;
    --vxe-ui-table-row-height-default: 36px;
    --vxe-ui-table-cell-padding-default: 8px;
    --vxe-ui-table-row-line-height: 20px;
    --vxe-ui-layout-background-color: transparent;
    --vxe-ui-base-popup-border-color: transparent;
    --vxe-ui-table-menu-background-color: rgba(0, 63, 107, 0.8);
    --vxe-ui-table-menu-item-width: auto;
    --vxe-ui-table-row-hover-background-color: transparent;
    --vxe-ui-table-row-current-background-color: transparent;
    --vxe-ui-table-row-hover-current-background-color: transparent;
    --vxe-ui-table-row-checkbox-checked-background-color: transparent;
    --vxe-ui-table-row-hover-checkbox-checked-background-color: transparent;
  }

  .vxe-table--context-menu-wrapper {
    border: none;
    border-radius: 4px;
    box-shadow: none;
    .vxe-context-menu--link {
      max-width: 200px;
      padding: 0 5px;
      text-align: center;
      .vxe-context-menu--link-prefix,
      .vxe-context-menu--link-suffix {
        min-width: 0;
      }
    }
    .vxe-context-menu--option-wrapper,
    .vxe-table--context-menu-clild-wrapper {
      border: none;
      position: relative;
      &:not(:last-child)::after {
        content: '';
        position: absolute;
        display: block;
        height: 1px;
        left: 12px;
        right: 12px;
        background-color: #7e8fa6;
      }

      li {
        margin: 0;
        border: none;
      }
      li.link--active {
        background: transparent;
        border: none;

        .vxe-context-menu--link {
          color: #ff811d;
        }
      }
    }
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 4px;
      padding: 1px;
      background: linear-gradient(to bottom, rgba(20, 186, 255, 0.68) 0, rgba(20, 186, 255, 0.68) 78%, rgba(122, 136, 203, 0) 100%);
      mask:
        linear-gradient(#000 0 0) content-box,
        linear-gradient(#000 0 0);
      mask-composite: exclude;
      pointer-events: none;
    }
  }

  .tree-grid {
    &.has-filter .tree-grid-wrapper {
      height: calc(100% - 40px);
    }

    .tree-grid-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.22), transparent);
      border-width: 1px;
      border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;
      height: 100%;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 4, 0.27);
        pointer-events: none;
        filter: blur(200px);
      }
    }
    .vxe-table.vxe-table--render-default {
      width: 100%;
      .vxe-tree--line {
        border-bottom-style: dotted;
        border-left-style: solid;
      }
      .vxe-body--row.row--current > .vxe-body--column .vxe-tree-cell .el-tooltip__trigger {
        background-color: #00355b;
        border-radius: 5px;
      }
      .vxe-body--row.row--hover > .vxe-body--column .vxe-tree-cell .el-tooltip__trigger {
        background-color: rgba(0, 53, 91, 0.5);
        border-radius: 5px;
      }
    }
    .vxe-cell--tree-node {
      height: 20px;
      .vxe-cell--tree-btn {
        width: 20px;
        top: 0;
        transform: none;
        i {
          font-size: 20px;
          color: #6b7078;
        }
      }
      &.is--active .vxe-cell--tree-btn i {
        color: #1a7aff;
      }
    }

    /** 设置连接线开始 */
    // 确保连接线显示完整
    .vxe-table--render-default .vxe-header--column:not(.col--active).col--ellipsis > .vxe-cell,
    .vxe-table--render-default .vxe-body--column:not(.col--active).col--ellipsis > .vxe-cell,
    .vxe-table--render-default .vxe-footer--column:not(.col--active).col--ellipsis > .vxe-cell {
      overflow: visible;
      & > .vxe-cell--wrapper {
        overflow: visible;
      }
    }

    .vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-row-group-cell,
    .vxe-table--render-default .vxe-body--column.col--ellipsis > .vxe-cell .vxe-tree-cell {
      overflow: visible;
    }

    [class*='row--level-']:not(.row--level-0):not(:has(.vxe-cell--tree-btn)) {
      .vxe-column-content-wrapper::before {
        content: '';
        position: absolute;
        height: 36px;
        width: 24px;
        left: -20px;
        bottom: 10px;
        border-left: 1px solid rgba(126, 143, 166, 0.5);
        border-bottom: 1px dotted rgba(126, 143, 166, 0.5);
      }

      &:has(.vxe-column-checkbox) .vxe-column-content-wrapper::before {
        left: -21px;
      }
    }

    // 降低第一个节点的连接线高度
    [class*='row--level-']:has(.vxe-cell--tree-btn) + [class*='row--level-'] {
      .vxe-column-content-wrapper::before {
        height: 24px;
      }
    }
    /** 设置连接线结束 */
  }
</style>
