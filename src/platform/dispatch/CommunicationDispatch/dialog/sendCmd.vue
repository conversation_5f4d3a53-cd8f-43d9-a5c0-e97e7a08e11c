<template>
  <bf-dialog
    v-model="visible"
    ref="sendCmd"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    :showHeader="false"
    class="header-border shadow-md shadow-slate-800 send-cmd-dialog drag-dialog"
    modal-class="drag-dialog-modal"
    append-to-body
    top="10vh"
    draggable
    center
  >
    <section class="flex h-full">
      <!-- <TableTree
        :ref="sendCmdTreeId"
        :treeId="sendCmdTreeId"
        :contextmenuOption="contextmenuOption"
        :option="treeOption"
        :filterOption="filterOption"
        class="send-command-tree"
        toolbar
        @select="selectNodes"
        @click="clickNode"
        @dblclick="dblclickNode"
        @loaded="treeLoaded"
      /> -->
      <dialog-table-tree
        ref="dialogTableTree"
        class="basis-[38%] h-full"
        :defaultCheckKeys="cmdTargetKeys"
        :disabled-rids="dialogTableTreeDisabledRids"
        @checkbox-change="onCheckboxChange"
      ></dialog-table-tree>
      <div class="w-[2px] h-[calc(100%_-_2px)] translate-y-[2px] bg-gradient-to-b from-[#6895FE] to-[#6EDBFF]"></div>
      <section class="send-cmd-content basis-[62%] flex flex-col p-4 pl-10">
        <div class="send-cmd-title p-[16px] pt-0 text-[28px] text-center">
          {{ $t('dialog.sendCmdTitle') }}
        </div>
        <el-radio-group v-model="cmdRadio" class="!grid grid-cols-3" @change="cmdRadioChange">
          <bf-radio value="cb42">
            <EllipsisText :content="$t('writeFreq.satellitePositionSwitch')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb01">
            <EllipsisText :content="$t('dialog.locateCtrl')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb02">
            <EllipsisText :content="$t('dialog.trailCtrl')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb03">
            <EllipsisText :content="$t('dialog.areaSearch')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb04">
            <EllipsisText :content="$t('dialog.electricFence')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb05">
            <EllipsisText :content="$t('dialog.postFence')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb06">
            <EllipsisText :content="$t('dialog.mobileCtrl')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb07">
            <EllipsisText :content="$t('dialog.alarmSet')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb10">
            <EllipsisText :content="$t('dialog.clearAlarm')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb08">
            <EllipsisText :content="$t('dialog.voiceCtrl')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb09">
            <EllipsisText :content="$t('dialog.telecontrol')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb21">
            <EllipsisText :content="$t('dialog.switchCh')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb31">
            <EllipsisText :content="$t('dialog.textMsg')" style="width: 120px" />
          </bf-radio>
          <bf-radio value="cb25" :disabled="!hasRfidAuth">
            <EllipsisText v-if="hasRfidAuth" :content="$t('dialog.gpsVirtualSet')" style="width: 120px" />
            <el-tooltip popper-class="bf-tooltip" v-else effect="dark" placement="top" :content="noRfidAuthToolTipText">
              <EllipsisText :content="$t('dialog.gpsVirtualSet')" style="width: 120px" />
            </el-tooltip>
          </bf-radio>
        </el-radio-group>

        <section class="flex-auto bg-[#0679CC75] shadow-[inset_0_0_18px_rgba(14,190,255,0.74)] mt-4 p-4">
          <!--卫星定位状态设置-->
          <el-form v-if="cmdRadio === 'cb42'" :model="cb42Cmd" label-position="left" class="cb42">
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-select v-model="cb42Cmd.code" class="!w-full" style="height: 50px">
                <el-option v-for="item in cb42CmdOptions" :key="item.value" :label="item.label" :value="item.value" />
              </bf-select>
            </el-form-item>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb42" @click="send_cb42_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--定位-->
          <el-form v-if="cmdRadio === 'cb01'" :model="cb01Cmd" label-position="left" class="cb01">
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.locateCount') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input-numberV2 v-model="cb01Cmd.count" :min="1" :max="9999" class="!w-full" style="height: 50px" />
            </el-form-item>
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.locateSpacing') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input-numberV2 v-model="cb01Cmd.spaceTime" :min="5" :max="9995" :step="5" class="!w-full" style="height: 50px" />
            </el-form-item>
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.size') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input-numberV2 v-model="cb01Cmd.size" :min="0" :max="495" :step="5" class="!w-full" style="height: 50px" />
            </el-form-item>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb01" @click="send_cb01_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--跟踪-->
          <el-form v-if="cmdRadio === 'cb02'" :model="cb02Cmd" label-position="left" class="cb02">
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-select v-model="cb02Cmd.track" @change="cb02Cmd_changed" class="!w-full" style="height: 50px">
                <el-option v-for="(item, index) in cb02Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
              </bf-select>
            </el-form-item>
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.trailSpacing') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input-numberV2 v-model="cb02Cmd.spaceTime" :min="5" :max="9995" :step="5" :disabled="disabled.cb02Cmd" class="!w-full" style="height: 50px" />
            </el-form-item>
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.size') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input-numberV2 v-model="cb02Cmd.size" :min="0" :max="495" :step="5" :disabled="disabled.cb02Cmd" class="!w-full" style="height: 50px" />
            </el-form-item>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb02" @click="send_cb02_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--区域查找-->
          <el-form v-if="cmdRadio === 'cb03'" :model="cb03Cmd" label-position="left" class="cb03">
            <div class="sendCmdHeightMax768">
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.minLon') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb03Cmd.minLon" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.minLat') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb03Cmd.minLat" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.maxLon') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb03Cmd.maxLon" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.maxLat') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb03Cmd.maxLat" class="!w-full" style="height: 50px" />
              </el-form-item>
            </div>
            <div class="flex justify-center gap-4">
              <bf-button color-type="primary" @click="get_lonLat_for_cb03">{{ $t('dialog.getLngLat') }}</bf-button>
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb03" @click="send_cb03_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--电子围栏-->
          <el-form v-if="cmdRadio === 'cb04'" :model="cb04Cmd" label-position="left" class="cbc04">
            <div class="sendCmdHeightMax768">
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-select v-model="cb04Cmd.setCmd" @change="cb04Cmd_changed" class="!w-full" style="height: 50px">
                  <el-option v-for="(item, index) in cb04Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
                </bf-select>
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.crossTOT') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input-numberV2 v-model="cb04Cmd.spaceTime" :min="1" :max="99" :disabled="disabled.cb04Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.minLon') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb04Cmd.minLon" :disabled="disabled.cb04Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.minLat') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb04Cmd.minLat" :disabled="disabled.cb04Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.maxLon') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb04Cmd.maxLon" :disabled="disabled.cb04Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.maxLat') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb04Cmd.maxLat" :disabled="disabled.cb04Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
            </div>
            <div class="flex justify-center gap-4">
              <bf-button color-type="primary" :disabled="disabled.cb04Cmd" @click="get_lonLat_for_cb04">
                {{ $t('dialog.getLngLat') }}
              </bf-button>
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb04" @click="send_cb04_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--岗哨围栏-->
          <el-form v-if="cmdRadio === 'cb05'" :model="cb05Cmd" label-position="left" class="cb05">
            <div class="sendCmdHeightMax768">
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-select v-model="cb05Cmd.setCmd" @change="cb05Cmd_changed" class="!w-full" style="height: 50px">
                  <el-option v-for="(item, index) in cb05Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
                </bf-select>
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.leaveTOT') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input-numberV2 v-model="cb05Cmd.spaceTime" :min="1" :max="99" :disabled="disabled.cb05Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.sentinelRadius') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input-numberV2 v-model="cb05Cmd.radius" :min="10" :max="100" :disabled="disabled.cb05Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.lon') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb05Cmd.lon" :disabled="disabled.cb05Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.lat') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb05Cmd.lat" :disabled="disabled.cb05Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
            </div>
            <div class="flex justify-center gap-4">
              <bf-button color-type="primary" :disabled="disabled.cb05Cmd" @click="get_lonLat_for_cb05">
                {{ $t('dialog.getLngLat') }}
              </bf-button>
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb05" @click="send_cb05_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--移动监控-->
          <el-form v-if="cmdRadio === 'cb06'" :model="cb06Cmd" label-position="left" class="cb06">
            <div class="sendCmdHeightMax768">
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-select v-model="cb06Cmd.setCmd" @change="cb06Cmd_changed" class="!w-full" style="height: 50px">
                  <el-option v-for="(item, index) in cb06Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
                </bf-select>
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.stopTOT') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input-numberV2 v-model="cb06Cmd.spaceTime" :min="1" :max="99" :disabled="disabled.cb06Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.lonDif') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input-numberV2 v-model="cb06Cmd.lonDif" :min="1" :max="99" :disabled="disabled.cb06Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.latDif') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input-numberV2 v-model="cb06Cmd.latDif" :min="1" :max="99" :disabled="disabled.cb06Cmd" class="!w-full" style="height: 50px" />
              </el-form-item>
            </div>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb06" @click="send_cb06_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--报警设置-->
          <el-form v-if="cmdRadio === 'cb07'" :model="cb07Cmd" label-position="left" class="cb07">
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-select v-model="cb07Cmd.setCmd" @change="cb07Cmd_changed" class="!w-full" style="height: 50px">
                <el-option v-for="(item, index) in cb07Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
              </bf-select>
            </el-form-item>
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.listenTime') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input-numberV2 v-model="cb07Cmd.jtTime" :min="0" :max="99" :disabled="disabled.cb07Cmd" class="!w-full" style="height: 50px" />
            </el-form-item>
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.locateSpacing') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input-numberV2 v-model="cb07Cmd.dwTime" :min="0" :max="99" :disabled="disabled.cb07Cmd" class="!w-full" style="height: 50px" />
            </el-form-item>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb07" @click="send_cb07_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--语音监控-->
          <el-form v-if="cmdRadio === 'cb08'" :model="cb08Cmd" label-position="left" class="cb08">
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-select v-model="cb08Cmd.setCmd" @change="cb08Cmd_changed" class="!w-full" style="height: 50px">
                <el-option v-for="(item, index) in cb08Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
              </bf-select>
            </el-form-item>
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.listenTime') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input-numberV2 v-model="cb08Cmd.jtTime" :min="1" :max="99" :disabled="disabled.cb08Cmd" class="!w-full" style="height: 50px" />
            </el-form-item>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb08" @click="send_cb08_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--遥开遥毙-->
          <el-form v-if="cmdRadio === 'cb09'" :model="cb09Cmd" label-position="left" class="cb09">
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-select v-model="cb09Cmd.setCmd" @change="cb09Cmd_changed" class="!w-full" style="height: 50px">
                <el-option v-for="(item, index) in cb09Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
              </bf-select>
            </el-form-item>
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.lockedStatus') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-select v-model="cb09Cmd.status" :disabled="disabled.cb09Cmd" class="!w-full" style="height: 50px">
                <el-option v-for="(item, index) in cb09Cmd_stats_options" :key="index" :label="$t(item.label)" :value="item.value" :disabled="item.disabled" />
              </bf-select>
            </el-form-item>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb09" @click="send_cb09_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--解除报警-->
          <el-form v-if="cmdRadio === 'cb10'" label-position="left" class="cb10">
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb10" @click="send_cb10_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--切换信道-->
          <el-form v-if="cmdRadio === 'cb21'" :model="cb21Cmd" label-position="left" class="cb21">
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.switchCh') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input-numberV2 v-model="cb21Cmd.channel" :min="1" :max="4095" class="!w-full" style="height: 50px" />
            </el-form-item>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb21" @click="send_cb21_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--文本短信-->
          <el-form v-if="cmdRadio === 'cb31'" :model="bc31Cmd" label-position="left" class="cb31">
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.smsType') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-select v-model="bc31Cmd.smsType" class="!w-full" style="height: 50px">
                <el-option v-for="(item, index) in smsTypeList" :key="index" :label="item.label" :value="item.value" />
              </bf-select>
            </el-form-item>
            <el-form-item>
              <template #label>
                <EllipsisText :content="$t('dialog.smsContent') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
              </template>
              <bf-input
                v-model="bc31Cmd.message"
                :maxlength="smsLenLimit"
                :autosize="{ minRows: 3, maxRows: 5 }"
                type="textarea"
                resize="none"
                class="!w-full"
              />
              <div
                class="text-gray-400 text-xs text-right sms-input-tips"
                :class="{
                  'text-red-500': bc31Cmd.message.length >= smsLenLimit,
                }"
              >
                {{ bc31Cmd.message.length }} / {{ smsLenLimit }}
              </div>
            </el-form-item>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.bc31" @click="send_bc31_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
          <!--GPS巡查点设置-->
          <el-form v-if="cmdRadio === 'cb25'" :model="cb25Cmd" label-position="left" class="cb25">
            <div class="sendCmdHeightMax768">
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-select v-model="cb25Cmd.setCmd" @change="cb25Cmd_changed" class="!w-full" style="height: 50px">
                  <el-option v-for="(item, index) in cb25Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
                </bf-select>
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.Gsource') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-select
                  v-model="cb25Cmd.gpsPoint"
                  filterable
                  clearable
                  :placeholder="$t('dialog.select')"
                  :disabled="disabled.cb25Cmd"
                  :no-match-text="$t('dialog.noMatchText')"
                  @change="cb25Cmd_gpsPoint_changed"
                  class="!w-full"
                  style="height: 50px"
                >
                  <el-option v-for="(item, index) in gpsPoints" :key="index" :label="item.label" :value="item.value" />
                </bf-select>
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.gpsCardNo') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb25Cmd.pointCard" :disabled="true" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.gpsRadius') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input v-model="cb25Cmd.radius" :disabled="true" class="!w-full" style="height: 50px" />
              </el-form-item>
              <el-form-item>
                <template #label>
                  <EllipsisText :content="$t('dialog.savePenNo') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
                </template>
                <bf-input-numberV2 v-model="cb25Cmd.pointN" :min="1" :max="20" :disabled="disabled.cb25Cmd_No" class="!w-full" style="height: 50px" />
              </el-form-item>
            </div>
            <div class="flex justify-center">
              <bf-button color-type="primary" :disabled="sendCmdBtns.cb25" @click="send_cb25_cmd">{{ $t('dialog.sendCmdTitle') }}</bf-button>
            </div>
          </el-form>
        </section>
      </section>
    </section>

    <!-- 获取经纬度和范围的地图 -->
    <template v-if="isFirstLoadBaseMap">
      <base-map ref="baseMap" v-model:visible="mapVisible" class="send-command-map-container" :controls="mapControls" @init="onInitMap" @close="onCloseMap">
        <template #topCenter>
          <div v-if="getOneLonLat" class="get-coordinate-tips">
            {{ $t('map.clickMapGetCoordinates') }}
          </div>
          <div v-else class="get-range-coordinates-tips" v-html="$t('map.selectCoordinatesTips', { iconEl: lonLatControlIconEl })" />
        </template>
      </base-map>
    </template>
  </bf-dialog>
</template>

<script>
  import bfprocess from '@/utils/bfprocess'
  import bftree from '@/utils/bftree'
  import bfutil, { checkTerminalIsSupportCommand, deferred, lonLatValidate, NotSupportCommandDeviceTypes } from '@/utils/bfutil'
  import { SelectLngLatControl } from '@/utils/map'
  import bfNotify from '@/utils/notify'
  import openDialog from '@/utils/dialog'
  import BfSpeaking from '@/platform/dispatch/CommunicationDispatch/dialog/bfSpeaking.vue'
  import qWebChannel from '@/utils/qWebChannelObj'
  import { checkLicenseAuthorized, checkLicenseWithModuleName, getAuthModuleI18nKey, getLicense, LicenseModuleNames } from '@/utils/bfAuth'
  import vueMixin from '@/utils/vueMixin'
  import BaseMap from '@/components/common/BaseMap.vue'
  import eventBus from '@/utils/eventBus'
  import { useRouteParams } from '@/router'
  import bfDialog from '@/components/bfDialog/main'
  import dialogTableTree from '@/components/common/dialogTableTree.vue'
  import bfInput from '@/components/bfInput/main'
  import bfInputNumberV2 from '@/components/bfInputNumber/main'
  import bfSelect from '@/components/bfSelect/main'
  import bfButton from '@/components/bfButton/main'
  import bfRadio from '@/components/bfRadio/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import { TreeNodeType } from '@/components/common/tableTree'

  let lngLatMap
  const lngLatMapReady = deferred()

  // 不支持组呼命令的cbxx指令
  const NotSupportGroupCallOperationCommands = ['cb42']
  const { getRouteParams } = useRouteParams()

  export default {
    name: 'BfSendcmd',
    mixins: [vueMixin],
    data() {
      return {
        isFirstLoadBaseMap: false,
        mapVisible: false,
        getOneLonLat: true,
        selectLngLatControl: new SelectLngLatControl(),
        qWebChannel,
        qWebServer: null,
        isInitTree: false,
        sendCmdTreeId: 'sendCmdTree',
        cmdTarget: {
          groud: [],
          device: [],
        },
        cmdRadio: 'cb01',
        gpsPoints: [],
        cb42Cmd: {
          // code [uint8]: 10:关闭 11:开启 12:查询
          code: 12,
        },
        cb01Cmd: {
          count: 1,
          spaceTime: 5,
          size: 20,
        },
        cb02Cmd: {
          track: 1,
          spaceTime: 5,
          size: 20,
        },
        cb02Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb03Cmd: {
          minLon: '',
          minLat: '',
          maxLon: '',
          maxLat: '',
        },
        cb04Cmd: {
          setCmd: 2,
          penN: 1,
          spaceTime: 1,
          minLon: '',
          minLat: '',
          maxLon: '',
          maxLat: '',
        },
        cb04Cmd_options: [
          {
            value: 0,
            label: 'dialog.removeFenceAllCtrl',
          },
          {
            value: 1,
            label: 'dialog.enableInCacnelOut',
          },
          {
            value: 2,
            label: 'dialog.enableOutCacnelIn',
          },
          // {value: 3, label: "dialog.enableInAndOut"},
          {
            value: 9,
            label: 'nav.enquiry',
          },
        ],
        cb05Cmd: {
          setCmd: 1,
          spaceTime: 1,
          lon: '',
          lonDif: '66',
          lat: '',
          latDif: '57',
          radius: 50,
        },
        cb05Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb06Cmd: {
          setCmd: 1,
          spaceTime: 1,
          lonDif: 65,
          latDif: 75,
        },
        cb06Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb07Cmd: {
          setCmd: 1,
          jtTime: 20,
          dwTime: 10,
        },
        cb07Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb08Cmd: {
          setCmd: 1,
          jtCh: 0,
          jtTime: 30,
        },
        voipServerConnected: false,
        cb08Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancelListen',
          },
          {
            value: 1,
            label: 'dialog.enableListen',
          },
        ],
        cb09Cmd: {
          setCmd: 1,
          status: 2,
        },
        cb09Cmd_options: [
          {
            value: 0,
            label: 'dataTable.powerOn',
          },
          {
            value: 1,
            label: 'dialog.lockedDev',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        cb09Cmd_stats_options: [
          {
            value: 0,
            label: 'dataTable.powerOn',
            disabled: false,
          },
          {
            value: 1,
            label: 'dialog.disListen',
            disabled: false,
          },
          {
            value: 2,
            label: 'dialog.disSend',
            disabled: false,
          },
          {
            value: 3,
            label: 'dialog.disSL',
            disabled: false,
          },
        ],
        cb21Cmd: {
          setCmd: 1,
          channel: 1,
        },
        cb21Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancelCenterCHCtrl',
          },
          {
            value: 1,
            label: 'dialog.openCenterCHCtrl',
          },
        ],
        bc31Cmd: {
          setCmd: 1,
          message: '',
          sendTime: '',
          codeTP: 2,
          smsType: '02',
        },
        cb25Cmd: {
          setCmd: 1,
          pointN: 1,
          pointCard: '',
          gpsPoint: '',
          radius: '',
          lon: '',
          lat: '',
          lonDif: '20',
          latDif: '20',
        },
        cb25Cmd_options: [
          {
            value: 0,
            label: 'dialog.cancel',
          },
          {
            value: 1,
            label: 'dialog.starting',
          },
          {
            value: 2,
            label: 'nav.enquiry',
          },
        ],
        sendCmdBtns: {
          cb01: false,
          cb02: false,
          cb03: false,
          cb04: false,
          cb05: false,
          cb06: false,
          cb07: false,
          cb08: false,
          cb09: false,
          cb10: false,
          cb11: false,
          cb21: false,
          cb24: false,
          cb25: false,
          bc31: false,
          cb42: false,
        },
        disabled: {
          cb02Cmd: false,
          cb04Cmd: false,
          cb05Cmd: false,
          cb06Cmd: false,
          cb07Cmd: false,
          cb08Cmd: false,
          cb09Cmd: false,
          cb25Cmd: false,
          cb25Cmd_No: false,
        },
        smsLenLimit: 140,
        dialogTableTreeDisabledRids: [],
      }
    },
    methods: {
      onInitMap(map) {
        lngLatMap = map
        lngLatMapReady.resolve(true)
      },
      onCloseMap() {
        this.mapVisible = false
        eventBus.emit('map-close')
      },
      onCheckboxChange(row, checked) {
        // row.rid 代表 device 或者 org 的 rid
        // checked 代表勾选或者取消勾选前的状态
        // checked = false 表示现在勾选了，checked = true 表示现在取消勾选了
        const rid = row.rid
        const nodeType = row.nodeType

        if (nodeType === 2) {
          // 处理设备节点 (Terminal)
          const device = bfglob.gdevices.get(rid)
          if (device) {
            const dmrId = device.dmrId
            if (!checked) {
              // 现在勾选了，添加到 device 数组中
              if (!this.cmdTarget.device.includes(dmrId)) {
                this.cmdTarget.device.push(dmrId)
              }
            } else {
              // 现在取消勾选了，从 device 数组中移除
              const index = this.cmdTarget.device.indexOf(dmrId)
              if (index > -1) {
                this.cmdTarget.device.splice(index, 1)
              }
            }
          }
        } else if (nodeType === 1) {
          // 处理组织节点 (Org)
          const orgItem = bfglob.gorgData.get(rid)
          if (orgItem) {
            const dmrId = orgItem.dmrId
            if (!checked) {
              // 现在勾选了，添加到 groud 数组中
              if (!this.cmdTarget.groud.includes(dmrId)) {
                this.cmdTarget.groud.push(dmrId)
              }
            } else {
              // 现在取消勾选了，从 groud 数组中移除
              const index = this.cmdTarget.groud.indexOf(dmrId)
              if (index > -1) {
                this.cmdTarget.groud.splice(index, 1)
              }
            }
          }
        }
      },
      openDlgFn() {
        this.qWebServer = this.qWebChannel.server
        if (bfglob.vspeaking) {
          this.voipServerConnected = bfglob.vspeaking.voipServerConnected
        }
      },

      selectAll(bool) {
        bftree.selectAll(this.sendCmdTreeId, bool)
      },
      // todo: remove this method,vxetreemanager will watch updateDeviceNodeTitle event
      // update_tree_node(device) {
      //   bftree.updateDeviceNodeTitle(this.sendCmdTreeId, device)
      // },

      get_gpsPoint_data_for_setGpsPoint() {
        const gpsPoints = []
        const g_linePoint = bfglob.glinePoints.getAll()
        for (const key in g_linePoint) {
          const item = g_linePoint[key]
          if (item.pointType !== 3) continue

          const gpsPoint = {
            value: item.pointRfid,
            label: item.pointId + '/' + item.pointName,
          }
          gpsPoints.push(gpsPoint)
        }

        this.gpsPoints = gpsPoints
      },
      // 禁用树节点不支持当前命令的选择功能
      disableNotSupportCommandNodes(cmdRadio = this.cmdRadio) {
        const newDeviceTarget = new Set()
        const notSupportGroupCallOperation = NotSupportGroupCallOperationCommands.includes(cmdRadio)
        const allNodes = this.$refs.dialogTableTree?.tableTreeRef.getTreeManager().treeRef.getTableData().visibleData
        if (notSupportGroupCallOperation) {
          const orgRids = allNodes.filter(node => node.nodeType === TreeNodeType.Org).map(node => node.rid)
          this.dialogTableTreeDisabledRids = orgRids
        } else {
          this.dialogTableTreeDisabledRids = []
        }
        const devicesRids = allNodes.filter(node => node.nodeType === TreeNodeType.Terminal).map(node => node.rid)
        for (let i = 0; i < devicesRids.length; i++) {
          const device = bfglob.gdevices.get(devicesRids[i])
          if (device) {
            const isSupported = checkTerminalIsSupportCommand(device.dmrId, cmdRadio)
            isSupported && newDeviceTarget.add(device.dmrId)
          }
          continue
        }

        // 重置当前选中的终端目标
        this.cmdTarget.device = this.cmdTarget.device.filter(item => newDeviceTarget.has(item))
        // 不支持组呼命令
        if (notSupportGroupCallOperation) {
          this.cmdTarget.groud = []
        }
      },
      cmdRadioChange(label) {
        if (label === 'cb25') {
          this.get_gpsPoint_data_for_setGpsPoint()
        }

        this.disableNotSupportCommandNodes(this.cmdRadio)
      },
      cb02Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb02Cmd = false
        } else {
          this.disabled.cb02Cmd = true
        }
      },
      cb04Cmd_changed(val) {
        if (val === 0 || val === 9) {
          this.disabled.cb04Cmd = true
        } else {
          this.disabled.cb04Cmd = false
        }
      },
      cb05Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb05Cmd = false
        } else {
          this.disabled.cb05Cmd = true
        }
      },
      cb06Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb06Cmd = false
        } else {
          this.disabled.cb06Cmd = true
        }
      },
      cb07Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb07Cmd = false
        } else {
          this.disabled.cb07Cmd = true
        }
      },
      cb08Cmd_changed(val) {
        if (val === 1) {
          this.disabled.cb08Cmd = false
        } else {
          this.disabled.cb08Cmd = true
        }
      },
      cb09Cmd_changed(val) {
        this.disabled.cb09Cmd = false
        if (val === 0) {
          this.cb09Cmd.status = 0
          this.cb09Cmd_stats_options[0].disabled = false
          this.cb09Cmd_stats_options[1].disabled = true
          this.cb09Cmd_stats_options[2].disabled = true
          this.cb09Cmd_stats_options[3].disabled = true
        } else if (val === 1) {
          this.cb09Cmd.status = 2
          this.cb09Cmd_stats_options[0].disabled = true
          this.cb09Cmd_stats_options[1].disabled = false
          this.cb09Cmd_stats_options[2].disabled = false
          this.cb09Cmd_stats_options[3].disabled = false
        } else if (val === 2) {
          this.cb09Cmd.status = 2
          this.disabled.cb09Cmd = true
        }
      },
      cb25Cmd_changed(val) {
        if (val === 2) {
          this.disabled.cb25Cmd = true
        } else {
          this.disabled.cb25Cmd = false
        }
        if (val === 0) {
          this.disabled.cb25Cmd_No = true
        } else {
          this.disabled.cb25Cmd_No = false
        }
      },
      cb25Cmd_gpsPoint_changed(pointRfid) {
        if (!pointRfid) {
          this.cb25Cmd.pointCard = ''
          this.cb25Cmd.radius = ''
          this.cb25Cmd.lon = ''
          this.cb25Cmd.lat = ''
        }
        var gpsPoint = bfglob.glinePoints.getDataByIndex(pointRfid)
        if (gpsPoint) {
          this.cb25Cmd.pointCard = gpsPoint.pointRfid
          this.cb25Cmd.radius = gpsPoint.gpsPointRadius
          this.cb25Cmd.lon = gpsPoint.lon
          this.cb25Cmd.lat = gpsPoint.lat
        }
      },
      // 跳转首页选择坐标范围
      get_lonLat_func(lonLatObj) {
        this.isFirstLoadBaseMap = true
        this.mapVisible = true
        this.getOneLonLat = false

        this.selectLngLatControl.enable(true)
        // 重写控件的select事件
        this.selectLngLatControl.select = data => {
          lonLatObj.minLon = data.minLon
          lonLatObj.minLat = data.minLat
          lonLatObj.maxLon = data.maxLon
          lonLatObj.maxLat = data.maxLat

          this.$refs.baseMap.close()
        }
      },
      get_lonLat_for_cb03() {
        this.get_lonLat_func(this.cb03Cmd)
      },
      get_lonLat_for_cb04() {
        this.get_lonLat_func(this.cb04Cmd)
      },
      get_lonLat_for_cb05() {
        this.isFirstLoadBaseMap = true
        this.mapVisible = true
        this.getOneLonLat = true
        this.selectLngLatControl.enable(false)

        const finish = () => {
          this.mapVisible = false
          lngLatMap.getCanvas().style.cursor = ''
        }
        const setLngLat = lngLat => {
          this.cb05Cmd.lon = lngLat.lng
          this.cb05Cmd.lat = lngLat.lat
        }
        const mapOnClick = evt => {
          setLngLat(evt.lngLat)
          onMapClose()
        }

        const onMapClose = () => {
          lngLatMap.off('click', mapOnClick)
          finish()
        }

        lngLatMapReady.then(() => {
          eventBus.once('map-close', onMapClose)
          lngLatMap.getCanvas().style.cursor = 'crosshair'
          lngLatMap.on('click', mapOnClick)
        })
      },

      // 发送命令后禁用按钮，默认3秒
      disabledSendCmdFunc(btn, sec = 3000) {
        this.sendCmdBtns[btn] = true
        setTimeout(
          function () {
            this.sendCmdBtns[btn] = false
          }.bind(this),
          sec
        )
      },
      send_cb42_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        bfprocess.cb42(this.cmdTarget, this.cb42Cmd)
        this.disabledSendCmdFunc('cb42')
      },
      send_cb01_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        const _size = (this.cb01Cmd.size / 5).toFixed(0) * 5
        const _spaceTime = (this.cb01Cmd.spaceTime / 5).toFixed(0) * 5
        this.cb01Cmd.size = parseInt(_size)
        this.cb01Cmd.spaceTime = parseInt(_spaceTime)
        bfprocess.cb01(this.cmdTarget, this.cb01Cmd)
        this.disabledSendCmdFunc('cb01')
      },
      send_cb02_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        const _size = (this.cb02Cmd.size / 5).toFixed(0) * 5
        const _spaceTime = (this.cb02Cmd.spaceTime / 5).toFixed(0) * 5
        this.cb02Cmd.size = parseInt(_size)
        this.cb02Cmd.spaceTime = parseInt(_spaceTime)
        bfprocess.cb02(this.cmdTarget, this.cb02Cmd)
        this.disabledSendCmdFunc('cb02')
      },
      send_cb03_cmd() {
        if (this.cb03Cmd.minLon === '' || this.cb03Cmd.minLat === '' || this.cb03Cmd.maxLon === '' || this.cb03Cmd.maxLat === '') {
          bfNotify.warningBox(this.$t('msgbox.selectLngLat'), 'error')
          return
        }

        // 可能输入非数字字符串
        if (isNaN(this.cb03Cmd.minLon) || isNaN(this.cb03Cmd.minLat) || isNaN(this.cb03Cmd.maxLon) || isNaN(this.cb03Cmd.maxLat)) {
          bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
          return
        }

        // 经纬度可能是用户输入数字字符串，需要转换为数字
        ;['minLon', 'minLat', 'maxLon', 'maxLat'].forEach(key => {
          this.cb03Cmd[key] = +this.cb03Cmd[key]
        })

        if (!lonLatValidate([this.cb03Cmd.minLon, this.cb03Cmd.minLat]) || !lonLatValidate([this.cb03Cmd.maxLon, this.cb03Cmd.maxLat])) {
          bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
          return
        }

        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          this.selectAll(true)
        }
        bfprocess.cb03(this.cmdTarget, this.cb03Cmd)
        this.disabledSendCmdFunc('cb03')
      },
      send_cb04_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        if (this.cb04Cmd.setCmd !== 9 && this.cb04Cmd.setCmd !== 0) {
          if (this.cb04Cmd.minLon === '' || this.cb04Cmd.minLat === '' || this.cb04Cmd.maxLon === '' || this.cb04Cmd.maxLat === '') {
            bfNotify.warningBox(this.$t('msgbox.selectLngLat'), 'error')
            return
          }

          // 可能输入非数字字符串
          if (isNaN(this.cb04Cmd.minLon) || isNaN(this.cb04Cmd.minLat) || isNaN(this.cb04Cmd.maxLon) || isNaN(this.cb04Cmd.maxLat)) {
            bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
            return
          }

          // 经纬度可能是用户输入数字字符串，需要转换为数字
          ;['minLon', 'minLat', 'maxLon', 'maxLat'].forEach(key => {
            this.cb04Cmd[key] = +this.cb04Cmd[key]
          })

          if (!lonLatValidate([this.cb04Cmd.minLon, this.cb04Cmd.minLat]) || !lonLatValidate([this.cb04Cmd.maxLon, this.cb04Cmd.maxLat])) {
            bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
            return
          }
        }

        bfprocess.cb04(this.cmdTarget, this.cb04Cmd)
        this.disabledSendCmdFunc('cb04')
      },
      send_cb05_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        if (this.cb05Cmd.setCmd === 1) {
          if (this.cb05Cmd.lon === '' || this.cb05Cmd.lat === '') {
            bfNotify.warningBox(this.$t('msgbox.selectLngLat'), 'error')
            return
          }

          // 可能输入非数字字符串
          if (isNaN(this.cb05Cmd.lon) || isNaN(this.cb05Cmd.lat)) {
            bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
            return
          }

          // 经纬度可能是用户输入数字字符串，需要转换为数字
          ;['lon', 'lat'].forEach(key => {
            this.cb05Cmd[key] = +this.cb05Cmd[key]
          })

          if (!lonLatValidate([this.cb05Cmd.lon, this.cb05Cmd.lat])) {
            bfNotify.warningBox(this.$t('msgbox.invalidLngLat'), 'error')
            return
          }
        }

        // 计算岗哨半径的经纬度差
        const latDiff = bfutil.calculateLatitudeRadiusDiff(this.cb05Cmd.radius)
        const lonDiff = bfutil.calculateLongitudeRadiusDiff(this.cb05Cmd.lat, this.cb05Cmd.radius)
        this.cb05Cmd.latDif = '' + bfutil.lngLatDiffLimit(latDiff)
        this.cb05Cmd.lonDif = '' + bfutil.lngLatDiffLimit(lonDiff)

        bfprocess.cb05(this.cmdTarget, this.cb05Cmd)
        this.disabledSendCmdFunc('cb05')
      },
      send_cb06_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        this.cb06Cmd.latDif = parseInt(this.cb06Cmd.latDif)
        this.cb06Cmd.lonDif = parseInt(this.cb06Cmd.lonDif)
        bfprocess.cb06(this.cmdTarget, this.cb06Cmd)
        this.disabledSendCmdFunc('cb06')
      },
      send_cb07_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        bfprocess.cb07(this.cmdTarget, this.cb07Cmd)
        this.disabledSendCmdFunc('cb07')
      },
      // cb08 语音监听，只能选呼
      sendCb08Cmd() {
        bfprocess.cb08(this.cmdTarget, this.cb08Cmd)
        this.disabledSendCmdFunc('cb08')
      },
      checkVoipServerOpenForSendCb08(count = 0) {
        // 检查是否已经打开了 BfSpeaking 对话框
        if (bfglob.vspeaking && bfglob.vspeaking.visible) {
          // 如果已经打开，直接检查连接状态并发送命令
          if (this.voipServerConnected) {
            this.sendCb08Cmd()
          } else {
            setTimeout(() => {
              if (++count < 20) {
                this.checkVoipServerOpenForSendCb08(count)
              }
            }, 500)
          }
        } else {
          // 如果没有打开，则打开对话框
          openDialog(BfSpeaking).then(vm => {
            if (this.voipServerConnected) {
              this.sendCb08Cmd()
            } else {
              setTimeout(() => {
                if (++count < 20) {
                  this.checkVoipServerOpenForSendCb08(count)
                }
              }, 500)
            }
          })
        }
      },
      send_cb08_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        // 如果系统中心控制台未运行，则禁止发命令
        if (!this.qWebServer) {
          bfNotify.warningBox(this.$t('msgbox.cannotSendCb08Cmd'), 'warning')
          return
        }

        if (!bfglob.userInfo.setting.voipSpeakInfo || !bfglob.userInfo.setting.voipSpeakInfo.speaker) {
          bfNotify.warningBox(this.$t('msgbox.notSetNetworkCallAgent'), 'warning')
          return
        }

        // this.checkVoipServerOpenForSendCb08()
        this.sendCb08Cmd()
      },
      send_cb09_cmd() {
        if (this.cmdTarget.groud.length > 0) {
          bfNotify.warningBox(this.$t('msgbox.canNotDeliveredByGroup'), 'error')
          return
        }
        if (this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        bfprocess.cb09(this.cmdTarget, this.cb09Cmd)
        this.disabledSendCmdFunc('cb09')
      },
      send_cb10_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        bfprocess.cb10(this.cmdTarget)
        this.disabledSendCmdFunc('cb10')
      },
      send_cb21_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        bfprocess.cb21(this.cmdTarget, this.cb21Cmd)
        this.disabledSendCmdFunc('cb21')
      },
      send_bc31_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }
        if (!this.bc31Cmd.message) {
          bfNotify.warningBox(this.$t('msgbox.smsNotEmpty'), 'warning')
          return
        }

        bfprocess.bc31(this.cmdTarget, this.bc31Cmd)
        this.disabledSendCmdFunc('bc31')
      },
      send_cb25_cmd() {
        if (this.cmdTarget.groud.length === 0 && this.cmdTarget.device.length === 0) {
          bfNotify.warningBox(this.$t('msgbox.selectTarget'), 'error')
          return
        }

        if ((this.cb25Cmd.setCmd === 0 || this.cb25Cmd.setCmd === 1) && !this.cb25Cmd.gpsPoint) {
          bfNotify.warningBox(this.$t('msgbox.selectGpsPoint'), 'error')
          return
        }
        // 计算岗哨半径的经纬度差
        this.cb25Cmd.latDif = '' + bfutil.calculateLatitudeRadiusDiff(this.cb25Cmd.radius)
        this.cb25Cmd.lonDif = '' + bfutil.calculateLongitudeRadiusDiff(this.cb25Cmd.lat, this.cb25Cmd.radius)

        bfprocess.cb25(this.cmdTarget, this.cb25Cmd)
        this.disabledSendCmdFunc('cb25')
      },
      updateServerConnectedStatus(val) {
        this.voipServerConnected = val
      },

      // rangeDeviation() {
      //   this.mapVisible = true
      //   // 触发子组件baseMap的绘制当前设备的标记点和所选经纬度返回的矩形
      //   this.$refs.baseMap.drawPointAndRange()
      // }
    },
    computed: {
      cmdTargetKeys() {
        return [...this.cmdTarget.groud, ...this.cmdTarget.device]
      },
      cb42CmdOptions() {
        // code [uint8]: 10:关闭 11:开启 12:查询
        return [
          { label: this.$t('dialog.close'), value: 10 },
          { label: this.$t('dialog.turnOn'), value: 11 },
          { label: this.$t('dialog.query'), value: 12 },
        ]
      },
      lonLatControlIconEl() {
        return '<span class="mdi mdi-map-marker-radius text-xl"></span>'
      },
      mapControls() {
        return [
          {
            id: 'selectLngLatControl',
            position: 'top-right',
            control: this.selectLngLatControl,
          },
        ]
      },
      defaultWidth() {
        return '120px'
      },
      defaultLabelWidth() {
        return this.isFR || this.isEN ? '180px' : this.defaultWidth
      },
      spaceTimeLabelWidth() {
        return this.isFR ? '160px' : this.defaultWidth
      },
      cb25PointLabelWidth() {
        return this.isFR ? '230px' : this.defaultLabelWidth
      },
      // 是否有RFID巡查授权
      hasRfidAuth() {
        const license = getLicense()
        if (!checkLicenseAuthorized(license)) {
          return false
        }
        return checkLicenseWithModuleName(license.lic?.licenses ?? {}, LicenseModuleNames.ModRfid)
      },
      noRfidAuthToolTipText() {
        const i18nKey = getAuthModuleI18nKey(LicenseModuleNames.ModRfid)
        return this.$t('auth.noSpecifiedModuleAuth', { module: this.$t(i18nKey) })
      },

      smsTypeList() {
        return [
          {
            label: this.$t('dialog.textInfo'),
            value: '02',
          },
          {
            label: this.$t('dialog.autoPlaySms'),
            value: '12',
          },
        ]
      },
    },
    watch: {
      'qWebChannel.server': {
        immediate: true,
        handler(val) {
          this.qWebServer = val
        },
      },
    },
    components: {
      BaseMap,
      bfDialog,
      dialogTableTree,
      bfInput,
      bfInputNumberV2,
      bfSelect,
      bfButton,
      bfRadio,
      EllipsisText,
    },
    mounted() {
      bfglob.vsendcmd = this
      this.openDlgFn()
      // bfglob.on('updateDeviceNodeTitle', this.update_tree_node)
      // 监听重新过滤在线设备事件
      // bfglob.on('voipServerConnected', this.updateServerConnectedStatus)

      // bfglob.on('rangeDeviation', this.rangeDeviation)
    },
    beforeUnmount() {
      lngLatMap = undefined
      bfglob.off('updateDeviceNodeTitle', this.update_tree_node)
      bfglob.off('voipServerConnected', this.updateServerConnectedStatus)

      // bfglob.off('rangeDeviation', this.rangeDeviation)
    },
    activated() {
      this.$route.params = getRouteParams(this.$route.name)
      if (this.$route.params.isSelect) {
        this.cmdRadio = this.$route.params.radio
        setTimeout(() => {
          bftree.selectAll('sendCmdTree', false)
          const node = bftree.getTreeNodeByRid('sendCmdTree', this.$route.params.target)
          if (!node) {
            return
          }
          node.setSelected(true)
          node.makeVisible({ scrollIntoView: true })
        })
      }
    },
  }
</script>

<style lang="scss">
  .send-cmd-dialog.el-dialog {
    --el-dialog-width: 900px;
    height: 80vh;
    padding: 0;

    .dialog-tree-wrapper {
      padding: 16px;
    }

    .send-cmd-content {
      padding: 16px;
      .send-cmd-title {
        background: linear-gradient(180deg, rgba(81, 224, 255, 0.52) 0%, rgba(81, 224, 255, 0.52) 22.66%, rgba(255, 255, 255, 0.52) 62.72%), #ffffff;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        text-shadow: 0px 0px 48px rgba(9, 171, 235, 0.61);
      }
    }
  }

  .send-command-map-container {
    position: fixed !important;
    width: 100vw !important;
    height: 100vh !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 9999 !important;
    background: rgba(0, 0, 0, 0.5);
  }

  .send-command-map-container .base-map-container {
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
  }

  .get-coordinate-tips,
  .get-range-coordinates-tips {
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    white-space: nowrap;
    user-select: none;
  }

  .vxe-table--context-menu-wrapper {
    z-index: 9999 !important;
  }
</style>
