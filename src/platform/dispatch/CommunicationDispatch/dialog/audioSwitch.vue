<template>
  <bf-dialog
    v-model="visible"
    ref="audioSwitch"
    :title="$t('dialog.selectListenGroup')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    class="header-border shadow-md shadow-slate-800 audio-switch-dialog drag-dialog"
    modal-class="drag-dialog-modal"
    append-to-body
    draggable
    center
  >
    <el-popover
      ref="listenGroupList"
      popper-class="listenGroupTooltip bf-tooltip"
      placement="left"
      :title="$t('dialog.listenGroup')"
      width="300"
      trigger="hover"
    >
      <div class="listenGroupList">
        <EllipsisText
          v-for="(memberName, idx) in listenGroupList"
          :key="memberName"
          class="listenGroupList-item !pt-[6px] !pb-[6px]"
          :content="`${idx + 1}: ${memberName}`"
        ></EllipsisText>
      </div>
      <template #reference>
        <p class="show-listen-list-icon absolute"><span class="bf-iconfont bfdx-tongzhixiaoxi text-[#1398E9]"></span></p>
      </template>
    </el-popover>
    <dialog-table-tree
      class="!w-[320px] h-full m-auto"
      :showOnlyOrg="true"
      :defaultCheckKeys="speakInfo.listenGroup"
      @checkbox-change="selectSendGroup"
    ></dialog-table-tree>
  </bf-dialog>
</template>

<script setup lang="ts">
  import { ref, watch, computed } from 'vue'
  import bfDialog from '@/components/bfDialog/main'
  import { setSpeakListenGroup, speakInfo, updateUserVoipSpeakInfo, globalVoipServerManager } from '@/utils/speak'
  import EllipsisText from '@/components/common/EllipsisText.vue'

  // 接收从openDialog传递的dialogVisible属性
  const props = defineProps<{
    dialogVisible?: boolean
  }>()

  // 定义emit事件，用于更新dialogVisible
  const emit = defineEmits<{
    'update:dialogVisible': [value: boolean]
  }>()

  // 内部状态
  const visible = ref(false)

  // 监听props.dialogVisible的变化
  watch(
    () => props.dialogVisible,
    newVal => {
      if (newVal !== undefined) {
        visible.value = newVal
      }
    },
    { immediate: true }
  )

  // 监听内部visible的变化，同步到父组件
  watch(visible, newVal => {
    emit('update:dialogVisible', newVal)
  })

  const selectSendGroup = (row, checked) => {
    const org = bfglob.gorgData.get(row.rid)
    if (!org || !org.dmrId) {
      return
    }

    const dmrId = org.dmrId
    const index = speakInfo.listenGroup.indexOf(dmrId)

    // checked 是勾选之前的状态，所以：
    // checked = false 表示即将勾选，需要添加到监听组
    // checked = true 表示即将取消勾选，需要从监听组移除
    if (!checked) {
      // 即将勾选，使用 setSpeakListenGroup 函数添加到监听组
      if (index === -1) {
        setSpeakListenGroup(row.rid)
        globalVoipServerManager.sl_updateListenGroup()
        updateUserVoipSpeakInfo()
      }
    } else {
      // 即将取消勾选，从监听组移除
      if (index > -1) {
        speakInfo.listenGroup.splice(index, 1)
        globalVoipServerManager.sl_updateListenGroup()
        updateUserVoipSpeakInfo()
      }
    }
  }

  const listenGroupList = computed(() => {
    return speakInfo.listenGroup.map(dmrId => {
      return (bfglob.gorgData.getDataByIndex(dmrId) || bfglob.noPermOrgData.getDataByIndex(dmrId))?.orgShortName ?? ''
    })
  })
</script>

<style lang="scss">
  .audio-switch-dialog.el-dialog {
    --el-dialog-width: 350px;
    height: 60vh;

    .el-dialog__body {
      .show-listen-list-icon {
        width: 32px;
        height: 32px;
        background-color: #1398e9;
        border-radius: 4px;
        line-height: 32px;
        text-align: center;
        position: absolute;
        margin: 0;
        top: 18px;
        left: 38px;
        cursor: pointer;

        span::before {
          font-size: 16px;
          color: #fff;
        }
      }
    }
  }

  .listenGroupTooltip {
    font-family: 'AlibabaPuHuiTi2';
    width: max-content !important;
    max-width: 320px;

    .el-popover__title {
      color: #fff;
      font-weight: bold;
      text-align: center;
      border-bottom: 1px solid #3095bd;
      padding-bottom: 6px;
    }

    .listenGroupList {
      max-height: 200px;
      overflow: auto;

      .listenGroupList-item {
        text-align: center;
        line-height: 20px;
      }
    }
  }
</style>
