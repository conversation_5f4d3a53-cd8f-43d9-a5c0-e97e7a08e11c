<script lang="ts" setup>
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import { DispatchRecentContactCardEmit } from './DispatchRecentContactCard.vue'
  import { calcScaleSize } from '@/utils/setRem'
  import { watch, ref, useTemplateRef } from 'vue'
  import { useRecentContact } from '@/utils/callContact'
  import { useResizeObserver } from '@vueuse/core'
  import { useI18n } from 'vue-i18n'
  import bfNotify from '@/utils/notify'

  const { t } = useI18n()
  const { recentContacts } = useRecentContact()

  const scrollerRef = useTemplateRef('scroller')

  watch(
    () => recentContacts.value,
    () => {
      scrollerRef.value?.updateVisibleItems(true)
    },
    { deep: true }
  )

  const emit = defineEmits<DispatchRecentContactCardEmit>()

  const itemSize = ref(calcScaleSize(115))
  const itemSecondarySize = ref(calcScaleSize(284))

  useResizeObserver(document.documentElement, () => {
    itemSize.value = calcScaleSize(115)
    itemSecondarySize.value = calcScaleSize(284)
  })

  const cleanRecentContacts = () => {
    if (recentContacts.value.length === 0) {
      bfNotify.messageBox(t('dispatch.noRecentContacts'))
      return
    }
    recentContacts.value.splice(0, recentContacts.value.length)
    bfNotify.messageBox(t('dispatch.deleteRecentContactsSuccess'))
  }
</script>

<template>
  <PageHeader :title="t('dispatch.recentContacts')">
    <DispatchTitleIcon icon="bfdx-qingchuyangshi-neibu" @click="cleanRecentContacts" />
  </PageHeader>
  <RecycleScroller
    class="recent-contact-container"
    ref="scroller"
    :items="recentContacts"
    :item-size="itemSize"
    :grid-items="2"
    :item-secondary-size="itemSecondarySize"
    key-field="keyId"
  >
    <template #default="{ item, index }">
      <DispatchRecentContactCard
        :key="index"
        v-bind="item"
        @call="targetDmrIDHex => emit('call', targetDmrIDHex)"
        @add-to-common="targetDmrIDHex => emit('addToCommon', targetDmrIDHex)"
        @remove-from-common="targetDmrIDHex => emit('removeFromCommon', targetDmrIDHex)"
      />
    </template>
  </RecycleScroller>
</template>

<style lang="scss" scoped>
  .recent-contact-container {
    height: 100%;
    padding: 10px 0 0;
    border: 1px solid transparent;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;

    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.38), rgba(0, 0, 11, 0.26));

    .card-container {
      margin: 10px;
    }
  }
</style>
