<script setup lang="ts">
  import { computed } from 'vue'
  import type { RecentContact } from '@/utils/callContact'

  const props = withDefaults(defineProps<RecentContact>(), {
    isCommonContact: false,
  })

  export type DispatchRecentContactCardEmit = {
    removeFromCommon: [targetDmrIDHex: string]
    addToCommon: [targetDmrIDHex: string]
    call: [targetDmrIDHex: string]
  }

  const emit = defineEmits<DispatchRecentContactCardEmit>()

  const srcIconClass = computed(() => {
    switch (props.srcType) {
      case 'sdcTerminal':
      case 'networkTerminal':
        return 'bfdx-duijiangjinei'
      default:
        return 'bfdx-duijiangjinei'
    }
  })

  const targetIconClass = computed(() => {
    switch (props.targetType) {
      case 'sdcTerminal':
      case 'networkTerminal':
        return 'bfdx-danhu'
      case 'group':
      case 'taskGroup':
        return 'bfdx-duijiangjizu'
      case 'fullCallContact':
        return 'bfdx-quanhu'
      default:
        return 'bfdx-danhu'
    }
  })
</script>

<template>
  <div class="card-container relative w-[274px] h-[90px] p-[10px] flex flex-col justify-center items-center">
    <div class="card-header h-[calc(50%+1px)] w-full border-b border-[#374A60]">
      <div class="src-contact pt-[5px] flex justify-center items-center gap-[5px]">
        <dispatch-recent-contact-card-icon :icon-class="srcIconClass" color="radial-gradient(#03dfff, #1677ff)" />
        <ellipsis-text class="w-[88px] text-center" not-wfull :content="props.srcName" />
        <dispatch-recent-contact-card-icon
          icon-class="bfdx-duijiangjitonghua"
          color="linear-gradient(to bottom right, #6895fe, #6edbff)"
          class="text-[rgba(0,0,0,0.2)]! mx-[5px]"
        />
        <dispatch-recent-contact-card-icon :icon-class="targetIconClass" color="linear-gradient(to bottom right, #6895fe, #6edbff)" class="mr-[5px]" />
        <ellipsis-text class="w-[39px] text-center" not-wfull :content="props.targetName" />
        <el-tooltip
          popper-class="bf-tooltip"
          placement="top"
          effect="dark"
          :content="$t(`dispatch.contactCard.${isCommonContact ? 'removeFromCommon' : 'addToCommon'}`)"
        >
          <!-- todo:根据isCommonContact调整css样式 -->
          <dispatch-recent-contact-card-icon
            icon-class="bfdx-duijiangjiqiehuan"
            class="ml-[5px] cursor-pointer active:opacity-50"
            @click="isCommonContact ? $emit('removeFromCommon', props.targetDmrIDHex) : emit('addToCommon', props.targetDmrIDHex)"
          />
        </el-tooltip>
      </div>
    </div>
    <div class="card-content h-[calc(50%-1px)] w-full pl-[38px] pr-[50px] flex justify-between items-center">
      <div class="flex gap-[11px]">
        <ellipsis-text class="text-center" not-wfull :content="props.soundTime" />
        <p class="text-[#FFAA19]">{{ props.soundLen + 's' }}</p>
      </div>
      <dispatch-recent-contact-card-icon
        icon-class="bfdx-duijiangjimaikefeng"
        color="linear-gradient(to bottom right, #42a4ff, #81c2ff)"
        class="cursor-pointer active:opacity-50"
        @click="emit('call', props.targetDmrIDHex)"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .card-container {
    background: url('@/assets/images/dispatch/contact_card/recent_contact_bg.svg') no-repeat center center;
    background-size: contain;
    font-size: 12px;
    font-weight: normal;
    color: #fff;
  }
</style>
