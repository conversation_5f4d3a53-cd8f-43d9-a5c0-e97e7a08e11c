<script lang="ts" setup>
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import type { DynamicContact } from '@/utils/callContact'
  import type { DispatchContactCardEmit } from './DispatchContactCard.vue'
  import { calcScaleSize } from '@/utils/setRem'
  import { computed, ref, useTemplateRef, watch } from 'vue'
  import { useResizeObserver } from '@vueuse/core'
  import { useI18n } from 'vue-i18n'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import bfDialog from '@/components/bfDialog/main'
  import validateRules from '@/utils/validateRules'
  import { v1 as uuid } from 'uuid'
  import {
    DbOrgIsVirtual,
    MemberType,
    DynamicGroupState,
    addDynamicGroup,
    checkPcDeviceJoinDynamicGroup,
    checkPcDeviceExitDynamicGroup,
  } from '@/utils/dynamicGroup/api'
  import BfCheckbox from '@/components/bfCheckbox/main'
  import BfInput from '@/components/bfInput/main'
  import BfRadio from '@/components/bfRadio/main'
  import BfButton from '@/components/bfButton/main'
  import DialogTableTree from '@/components/common/dialogTableTree.vue'
  import { TreeNodeType } from '@/components/common/tableTree/types'
  import bfutil from '@/utils/bfutil'
  import bfNotify from '@/utils/notify'
  import { speakInfo } from '@/utils/speak'
  import eventBus from '@/utils/eventBus'

  const dynamicType = {
    100: 'tempGroup',
    101: 'taskGroup',
    0: 'tempGroup',
    1: 'taskGroup',
  }

  const { t } = useI18n()
  const dynamicGroupList = computed<Array<DynamicContact>>(() => {
    const allDynamicData = bfutil.objToArray(bfglob.gorgData.getDynamicGroup())
    return allDynamicData.map(item => {
      const parentOrg = bfglob.gorgData.getDataMaybeNoPerm(item.parentOrgId)
      return {
        rid: item.rid,
        type: dynamicType[item.orgIsVirtual],
        targetRid: item.rid,
        dmrIDHex: item.dmrId,
        name: item.orgShortName,
        parentOrg: parentOrg?.orgShortName ?? item.parentOrgId,
        orgIsVirtual: item.orgIsVirtual,
        dynamicGroupState: item.dynamicGroupState,
      }
    })
  })
  const itemSize = ref(calcScaleSize(132))
  const itemSecondarySize = ref(calcScaleSize(228))
  const dynamicVisible = ref(false)

  const dialogTableTreeRef = useTemplateRef('dialogTableTreeRef')
  // 动态组表单相关
  const dynamicGroupFormRef = useTemplateRef('dynamicGroupForm')
  const isFastDynamicGroup = ref(false)

  const dynamicGroupData = ref({
    rid: uuid(),
    parentOrgId: bfglob?.userInfo?.orgId || '',
    orgShortName: '',
    dmrId: '',
    orgSortValue: 100,
    note: '',
    // 动态组类型 0:临时组->100 1：任务组->101
    orgIsVirtual: DbOrgIsVirtual.TempGroup,
  })

  const DynamicGroupMemberLimit = {
    100: 16,
    101: 128,
    0: 16,
    1: 128,
  }

  // 动态组成员数据
  const members = ref({
    devices: [],
    groups: [],
  })

  const treeCheckedDmrIds = computed(() => {
    const deviceDmrIds = members.value.devices.map(item => item.dmrId)
    const groupDmrIds = members.value.groups.map(item => item.dmrId)
    return [...deviceDmrIds, ...groupDmrIds]
  })

  const emit = defineEmits<DispatchContactCardEmit>()

  const scrollerRef = useTemplateRef('scroller')

  const getDynamicIsExpired = (item: DynamicContact) => {
    console.log('getDynamicIsExpired', item)
    return item.orgIsVirtual === DbOrgIsVirtual.TempGroup && item.dynamicGroupState === DynamicGroupState.Expired
  }

  const dynamicGroupStatus = computed(() => {
    // 1: 正常 10: 失效/删除中
    return {
      [DynamicGroupState.Normal]: t('dynamicGroup.normal'),
      [DynamicGroupState.Expired]: t('dynamicGroup.expired'),
    }
  })

  watch(
    () => dynamicGroupList.value,
    () => {
      scrollerRef.value?.updateVisibleItems(true)
    },
    { deep: true }
  )

  useResizeObserver(document.documentElement, () => {
    itemSize.value = calcScaleSize(132)
    itemSecondarySize.value = calcScaleSize(228)
  })

  const openNewDynamicGroupDialog = () => {
    // 重置表单数据
    dynamicGroupData.value = {
      rid: '',
      parentOrgId: bfglob?.userInfo?.orgId || '',
      orgShortName: '',
      dmrId: '',
      orgSortValue: 100,
      note: '',
      orgIsVirtual: DbOrgIsVirtual.TempGroup,
    }

    // 清空成员数据
    members.value = {
      devices: [],
      groups: [],
    }

    isFastDynamicGroup.value = false
    dynamicVisible.value = true
  }

  // 复选框变化处理
  const onCheckboxChange = (row, checked) => {
    // checked 代表勾选或者取消勾选前的状态
    // 选中
    if (!checked) {
      if (memberNodes.value.length >= maxSelectSize(dynamicGroupData.value.orgIsVirtual)) {
        dialogTableTreeRef.value.tableTreeRef.setCheckboxRowByRid(row.rid, false)
        return
      }
      if (row.nodeType === TreeNodeType.Org) {
        const org = bfglob.gorgData.getDataMaybeNoPerm(row.rid)
        if (org) {
          members.value.groups.push(org)
        }
      } else {
        const device = bfglob.gdevices.get(row.rid)
        if (device) {
          members.value.devices.push(device)
        }
      }
    } else {
      // 取消勾选
      if (row.nodeType === TreeNodeType.Org) {
        members.value.groups = members.value.groups.filter(item => item.rid !== row.rid)
      } else {
        members.value.devices = members.value.devices.filter(item => item.rid !== row.rid)
      }
    }
  }

  const quitOrDisbandGroup = () => {
    console.log('Quit or disband group')
  }

  const removeOrAddFrequently = () => {
    console.log('removeOrAddFrequently')
  }

  const editDynamicGroup = (dynamicContact: DynamicContact) => {
    const data = bfglob.gorgData.get(dynamicContact.rid)
    dynamicGroupData.value = data
    const dynamicDetail = bfglob.gdynamicGroupDetail
  }

  // 计算属性
  const memberNodes = computed(() => {
    const devices = members.value.devices || []
    const groups = members.value.groups || []
    return generateMemberInfo(groups, devices)
  })

  const generateMemberInfo = (groups, devices) => {
    const list = []
    for (let i = 0; i < devices.length; i++) {
      const device = devices[i]
      list.push({
        name: device.selfId,
        dmrId: device.dmrId,
        dmrIdLabel: `${device.dmrId} / ${Number(`0x${device.dmrId}`) & 0x7fffffff}`,
        rid: device.rid,
        detailRid: device.rid,
        isOrg: false,
      })
    }
    for (let i = 0; i < groups.length; i++) {
      const group = groups[i]
      list.push({
        name: group.orgShortName,
        dmrId: group.dmrId,
        dmrIdLabel: `${group.dmrId} / ${Number(`0x${group.dmrId}`) & 0x7fffffff}`,
        rid: group.rid,
        detailRid: group.rid,
        isOrg: true,
      })
    }
    return list
  }

  const rules = computed(() => {
    const orgShortNameRule = []
    if (!isFastDynamicGroup.value) {
      orgShortNameRule.unshift(validateRules.required())
    }

    return {
      orgShortName: orgShortNameRule,
    }
  })

  const orgIsVirtualChange = (val: DbOrgIsVirtual) => {
    if (val === DbOrgIsVirtual.TaskGroup) {
      isFastDynamicGroup.value = false
    }
    dynamicGroupData.value.orgIsVirtual = val
  }

  const getMemberOrgShortName = detail => {
    // 组成员，memberOrgId===rid，需要先查找到自己的数据，再读取上级名称
    let memberOrgId = detail.memberOrgId
    if (detail.isDeviceGroup === MemberType.Group) {
      memberOrgId = bfglob.gdynamicGroupDetail.getDetailSource(detail.rid)?.parentOrgId ?? ''
    }
    return bfglob.gorgData.getDataMaybeNoPerm(memberOrgId)?.orgShortName ?? '-'
  }

  const getMemberOrgName = member => {
    const detail = bfglob.gdynamicGroupDetail.get(member.detailRid)
    if (!detail) {
      if (!member.isOrg) {
        const dev = bfglob.gdevices.get(member.rid)
        if (!dev) return '-'
        return bfglob.gorgData.getShortName(dev.orgId) ?? '-'
      }
      const group = bfglob.gorgData.get(member.rid)
      if (!group) return '-'
      return bfglob.gorgData.get(group.parentOrgId)?.orgShortName ?? '-'
    }
    return getMemberOrgShortName(detail)
  }

  const clickMemberInfo = member => {}

  const removeMember = member => {
    if (member.isOrg) {
      members.value.groups = members.value.groups.filter(item => item.rid !== member.rid)
    } else {
      members.value.devices = members.value.devices.filter(item => item.rid !== member.rid)
    }
  }

  const maxSelectSize = type => {
    return DynamicGroupMemberLimit[type] || DynamicGroupMemberLimit[1]
  }

  const call = (targetDmrId, item, callback) => {
    console.log('call', targetDmrId)
    if (getDynamicIsExpired(item)) {
      bfNotify.messageBox(t('dynamicGroup.expiredDynamicGroup'), 'error')
      callback(false)
    } else {
      callback(true)
    }
  }

  const hangup = (targetDmrId, item, callback) => {
    console.log('hangup', targetDmrId)
    callback(true)
  }

  // 创建快捷临时组成功事件,当前组件订阅使用
  const addFastTempGroupSuccess = 'add-fast-temp-group-success'
  const mergeGroupDetail = (dataList, dynamicGroup) => {
    const dynamicGroupType = dynamicGroup.orgIsVirtual === 100 ? 0 : 1
    return (dataList || []).map(data => {
      return Object.assign(data, {
        rid: data.rid || uuid(),
        orgId: dynamicGroup.rid,
        dynamicGroupType,
      })
    })
  }

  const add = async data => {
    if (memberNodes.value.length <= 0) {
      bfNotify.message(t('dynamicGroup.noGroupMembers'), 'error')
      return
    }

    // 检测组成员是否超出限制
    if (memberNodes.value.length > maxSelectSize(data.orgIsVirtual)) {
      bfNotify.messageBox(t('dynamicGroup.groupMembersLimit'), 'error')
      return
    }

    const rid = uuid()
    const orgData = {
      ...data,
      orgIsVirtual: isFastDynamicGroup.value ? DbOrgIsVirtual.FastTempGroup : data.orgIsVirtual,
      rid,
      orgSortValue: 100,
      dmrId: '',

      // 补齐db_org表字段
      orgSelfId: rid,
      orgFullName: data.orgShortName,
      orgImg: '11111111-1111-1111-1111-111111111111',
      setting: '{}',
    }
    const modifyDeviceList = {
      dynamicGroup: orgData,
      userPriority: speakInfo.priority ?? 2,
      devices: mergeGroupDetail(members.value.devices, orgData),
      groups: mergeGroupDetail(members.value.groups, orgData),
    }

    const resRpcCmd = await addDynamicGroup(modifyDeviceList)
    bfglob.console.log('[addDynamicGroup] res:', resRpcCmd)

    if (resRpcCmd.resInfo === '+OK') {
      bfNotify.messageBox(t('msgbox.addSuccess'), 'success')
      // 添加动态组成功，返回动态组的dmrId
      Object.assign(orgData, resRpcCmd.body.dynamicGroup)
      // 如果是快捷临时组，发布对应的事件
      if (isFastDynamicGroup.value) {
        eventBus.emit(addFastTempGroupSuccess, orgData.dmrId)
        isFastDynamicGroup.value = false
      }

      // DefaultData.orgIsVirtual = DbOrgIsVirtual.FastTempGroup === orgData.orgIsVirtual ? DbOrgIsVirtual.TempGroup : DbOrgIsVirtual.TaskGroup
      dynamicGroupData.value.orgShortName = bfutil.customNumberIncrement(orgData.orgShortName, 1)

      // 每个组或终端只能加入一个动态组，所以需要清楚当前的选中节点
      // this.$refs.dynamicGroupTreeRef?.$selectAll(false)
      bfglob.emit('add_global_dynamic_group', orgData)
      bfglob.emit('dynamic-group-subscribe-server-command', orgData)

      // 发布表格数据变更事件
      // bfprocess.publishTableEvent(this.dataTable.name, 'add', orgData)

      // 处理动态组下成员
      const resDevices = resRpcCmd.body.devices
      const resGroups = resRpcCmd.body.groups
      bfglob.emit('add_global_dynamic_group_detail', [...resDevices, ...resGroups])
      resDevices.forEach(v => {
        checkPcDeviceJoinDynamicGroup(v).catch()
        checkPcDeviceExitDynamicGroup(v).catch()
      })

      // 添加查询日志
      const content = t('dialog.add') + orgData.orgShortName + t('dynamicGroup.title')
      bfglob.emit('addnote', content)

      // // 访问用户是否跳转联网通话
      // this.$nextTick(() => {
      //   this.gotoSpeakerPage(orgData)
      // })

      return orgData
    } else {
      if (commonOperationErr(resRpcCmd)) {
        return
      }
      bfNotify.messageBox(t('msgbox.addError'), 'error')
    }
  }

  const confirmAddDynamicGroup = () => {
    console.log('addDynamicGroup', dynamicGroupData.value)
    add(dynamicGroupData.value)
      .then(() => {
        members.value.devices = []
        members.value.groups = []
      })
      .catch(err => {
        bfglob.console.error('[addDynamicGroup] err:', err)
      })
  }

  const commonOperationErr = rpc_cmd_obj => {
    if (rpc_cmd_obj.resInfo.includes('db_org_org_self_id_key')) {
      bfNotify.warningBox(t('msgbox.repeatNo'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('db_org_dmr_id_key')) {
      // 服务器生成DMRID异常
      bfNotify.warningBox(t('msgbox.repeatDMRID'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('db_org_org_short_name_key')) {
      bfNotify.warningBox(t('dynamicGroup.repeatGroupName'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('no login info for this session')) {
      bfNotify.warningBox(t('msgbox.loginSessionExpired'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('DatabaseGetSystemDbString')) {
      bfNotify.warningBox(t('dynamicGroup.notFoundDbHandler'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('can not get new dmrid for group')) {
      bfNotify.warningBox(t('dynamicGroup.noDynamicGroupDmrId'))
      return true
    }
    if (
      rpc_cmd_obj.resInfo.includes('no such dynamic group') ||
      rpc_cmd_obj.resInfo.includes('no such dynamic group manager') ||
      rpc_cmd_obj.resInfo.includes('Can not find this dynamic group in manager')
    ) {
      bfNotify.warningBox(t('dynamicGroup.notFoundDynamicGroup'))
      return true
    }
    if (rpc_cmd_obj.resInfo.includes('Can not find this temp group in manager')) {
      bfNotify.warningBox(t('dynamicGroup.notFoundDynamicGroup'))
      return true
    }

    return false
  }
</script>

<template>
  <PageHeader :title="t('dispatch.dynamicGroup')">
    <DispatchTitleIcon icon="bfdx-xinzengyangshineibu" @click="openNewDynamicGroupDialog" />
  </PageHeader>
  <RecycleScroller
    ref="scroller"
    class="contact-container"
    :items="dynamicGroupList"
    :item-size="itemSize"
    :grid-items="2"
    :item-secondary-size="itemSecondarySize"
    center
    key-field="dmrIDHex"
  >
    <template #default="{ item, index }">
      <DispatchContactCard
        v-if="item.dmrIDHex"
        :class="{ 'opacity-50': getDynamicIsExpired(item) }"
        :key="index"
        v-bind="item"
        @locate="targetDmrId => emit('locate', targetDmrId)"
        @call="(targetDmrId, callback) => call(targetDmrId, item, callback)"
        @hangup="(targetDmrId, callback) => hangup(targetDmrId, item, callback)"
        @message="targetDmrId => emit('message', targetDmrId)"
        @send-command="targetDmrId => emit('sendCommand', targetDmrId)"
        @send-message="targetDmrId => emit('sendMessage', targetDmrId)"
      >
        <template #center-title>
          <ellipsis-text :content="item.orgIsVirtual === DbOrgIsVirtual.TaskGroup ? t('dynamicGroup.taskGroup') : t('dynamicGroup.tempGroup')" />
        </template>

        <template #title>
          <div class="flex justify-center items-center gap-2 rotate-180">
            <el-tooltip popper-class="bf-tooltip" :content="t('dialog.edit')" placement="top" effect="dark">
              <span class="leading-[14px] cursor-pointer bf-iconfont bfdx-duijiangjituichu before:text-[14px]" @click="editDynamicGroup(item)"></span>
            </el-tooltip>
            <el-tooltip popper-class="bf-tooltip" :content="t('dispatch.contactCard.addOrRemoveFrequently')" placement="top" effect="dark">
              <span class="leading-[14px] cursor-pointer bf-iconfont bfdx-duijiangjiqiehuan before:text-[14px]" @click="removeOrAddFrequently"></span>
            </el-tooltip>
            <el-tooltip popper-class="bf-tooltip" :content="t('dispatch.contactCard.quitOrDisband')" placement="top" effect="dark">
              <span class="leading-[14px] cursor-pointer bf-iconfont bfdx-duijiangjituichu before:text-[14px]" @click="quitOrDisbandGroup"></span>
            </el-tooltip>
          </div>
        </template>
      </DispatchContactCard>
    </template>
  </RecycleScroller>
  <bf-dialog
    v-model="dynamicVisible"
    :title="t('dynamicGroup.title')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="dynamic-group-dialog"
    width="720px"
    center
  >
    <div class="flex gap-4 h-[480px]">
      <!-- 左侧表单 -->
      <div class="flex flex-col flex-1">
        <el-form
          ref="dynamicGroupForm"
          :model="dynamicGroupData"
          label-position="top"
          :rules="rules"
          :validate-on-rule-change="false"
          class="flex-none !h-auto"
        >
          <el-form-item :label="t('dialog.type') + ':'" prop="orgIsVirtual">
            <div class="flex gap-4">
              <BfRadio v-model="dynamicGroupData.orgIsVirtual" :value="DbOrgIsVirtual.TaskGroup" @change="orgIsVirtualChange">
                {{ t('dynamicGroup.taskGroup') }}
              </BfRadio>
              <BfRadio v-model="dynamicGroupData.orgIsVirtual" :value="DbOrgIsVirtual.TempGroup" @change="orgIsVirtualChange">
                {{ t('dynamicGroup.tempGroup') }}
              </BfRadio>
            </div>
          </el-form-item>
          <el-form-item :label="t('dialog.name') + ':'" prop="orgShortName">
            <BfInput v-model="dynamicGroupData.orgShortName" :maxlength="16" />
          </el-form-item>
          <el-form-item>
            <BfCheckbox v-model="isFastDynamicGroup" :disabled="dynamicGroupData.orgIsVirtual !== DbOrgIsVirtual.TempGroup">
              {{ t('dynamicGroup.tempGroupInvalidAndDelete') }}
            </BfCheckbox>
          </el-form-item>
        </el-form>

        <!-- 成员信息显示 -->
        <div class="flex-1">
          <div class="text-sm font-medium mb-2">{{ t('dynamicGroup.memberDetails') + ':' }}</div>
          <div class="max-h-[220px] overflow-y-auto">
            <div v-if="memberNodes.length === 0" class="text-gray-500 text-center py-4">
              {{ t('dynamicGroup.noGroupMembers') }}
            </div>
            <div v-else class="space-y-2 members">
              <div v-for="member in memberNodes" :key="member.rid" class="member-item" :title="member.dmrIdLabel" @click="clickMemberInfo(member)">
                <span class="member-item-name">
                  {{ getMemberOrgName(member) + ' / ' + member.name }}
                </span>
                <span class="bf-iconfont bfdx-biaogeshanchushannan before:text-[#FDA216]" @click.stop="removeMember(member)"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧树形选择 -->
      <div class="w-[360px] h-[480px] pl-4">
        <div class="text-end h-[22px]">{{ memberNodes.length }}/{{ maxSelectSize(dynamicGroupData.orgIsVirtual) }}</div>
        <DialogTableTree class="!h-[458px]" ref="dialogTableTreeRef" :default-check-keys="treeCheckedDmrIds" @checkbox-change="onCheckboxChange" />
      </div>
    </div>

    <template #footer>
      <div class="flex justify-center gap-3">
        <BfButton color-type="info">
          {{ t('dialog.cancel') }}
        </BfButton>
        <BfButton color-type="primary" @click="confirmAddDynamicGroup">
          {{ t('dialog.confirm') }}
        </BfButton>
      </div>
    </template>
  </bf-dialog>
</template>

<style lang="scss" scoped>
  .contact-container {
    height: 100%;
    padding: 10px 53px 0;
    border: 1px solid transparent;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;

    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.38), rgba(0, 0, 11, 0.26));
    .card-container {
      margin: 10px;
    }
  }

  .dynamic-group-dialog.el-dialog {
    .members {
      padding-left: 16px;
      width: 100%;
      overflow: auto;

      .member-item {
        line-height: 20px;
        flex-basis: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
          .member-item-name,
          .member-item-dmrId {
            color: #20a0ff;
          }
        }

        .member-item-name {
          flex: auto;
          margin-right: 10px;
        }

        .remove-member-action {
          margin-left: 6px;
          cursor: pointer;
          color: #f56c6c;
          font-size: 16px;
        }
      }

      &.is-disabled {
        .member-item {
          cursor: default;

          &:hover {
            .member-item-name,
            .member-item-dmrId {
              color: inherit;
            }
          }

          .remove-member-action {
            display: none;
          }
        }
      }
    }
  }
</style>
