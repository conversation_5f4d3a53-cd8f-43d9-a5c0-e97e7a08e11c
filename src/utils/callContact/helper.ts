/** 通话联系人管理相关操作，GroupCallContacts and SingleCallContacts */

import dbCmd from '@/modules/protocol/db.pb.cmd'
import bfproto from '@/modules/protocol'
import { getDbSubject, DeviceTypes } from '@/utils/bfutil'
import type { CallContactEvent, Contact, ContactGroupType, ContactTerminalType } from './types'
import { DbOrgIsVirtual } from '@/utils/dynamicGroup/api'
import { useCommonContact } from './commonContact'
import { v7 as uuid } from 'uuid'
import bfTime from '@/utils/time'

export function getDbGroupCallContactsFromServer(userRid: string) {
  const msgObj = {
    userRid: userRid,
  }
  const msgOpts = {
    rpcCmdFields: {
      origReqId: 'user_rid',
      resInfo: '*',
    },
    decodeMsgType: 'db_group_call_contacts_list',
  }

  bfproto
    .sendMessage(dbCmd.DB_GROUP_CALL_CONTACTS_GETBY, msgObj, 'db_group_call_contacts', getDbSubject(), msgOpts)
    .then(rpc_cmd_obj => {
      const db_group_call_contacts_list_obj = rpc_cmd_obj.body
      if (typeof db_group_call_contacts_list_obj.rows === 'undefined' || db_group_call_contacts_list_obj.rows.length === 0) {
        return
      }
      const rows = db_group_call_contacts_list_obj.rows
      for (const i in rows) {
        const item = rows[i]
        addDbGroupCallContact(item)
      }
      bfglob.console.log('获取用户单位通话联系人成功', rows)
    })
    .catch(err => {
      bfglob.console.warn('获取用户单位通话联系人失败', err)
    })
}

export function getDbSingleCallContactsFromServer(userRid: string) {
  const msgObj = {
    userRid: userRid,
  }
  const msgOpts = {
    rpcCmdFields: {
      origReqId: 'user_rid',
      resInfo: '*',
    },
    decodeMsgType: 'db_single_call_contacts_list',
  }

  bfproto
    .sendMessage(dbCmd.DB_SINGLE_CALL_CONTACTS_GETBY, msgObj, 'db_single_call_contacts', getDbSubject(), msgOpts)
    .then(rpc_cmd_obj => {
      const db_single_call_contacts_list_obj = rpc_cmd_obj.body
      if (typeof db_single_call_contacts_list_obj.rows === 'undefined' || db_single_call_contacts_list_obj.rows.length === 0) {
        return
      }
      const rows = db_single_call_contacts_list_obj.rows
      for (const i in rows) {
        const item = rows[i]
        addDbSingleCallContact(item)
      }
      bfglob.console.log('获取用户终端通话联系人成功', rows)
    })
    .catch(err => {
      bfglob.console.warn('获取用户终端通话联系人失败', err)
    })
}

export function createContactFromDbGroupCallContact(data): Contact | null {
  const orgItem = bfglob.gorgData.get(data.orgId)
  if (!orgItem) {
    return null
  }

  const parentOrgItem = bfglob.gorgData.get(orgItem.parentOrgId)

  return {
    rid: data.rid,
    type: getGroupContactType(orgItem.dmrID, orgItem.orgIsVirtual),
    parentOrg: parentOrgItem?.orgShortName || '',
    name: orgItem.orgShortName,
    dmrIDHex: orgItem.dmrId,
    targetRid: orgItem.rid,
    sortValue: data.sortValue,
  } satisfies Contact
}

export function createContactFromDbSingleCallContact(data): Contact | null {
  const deviceItem = bfglob.gdevices.get(data.deviceId)
  if (!deviceItem) {
    return null
  }

  const parentOrgItem = bfglob.gorgData.get(deviceItem.orgId)

  return {
    rid: data.rid,
    type: getSingleContactType(deviceItem.deviceType),
    parentOrg: parentOrgItem?.orgShortName || '',
    name: deviceItem.selfId,
    dmrIDHex: deviceItem.dmrId,
    targetRid: deviceItem.rid,
    sortValue: data.sortValue,
  } satisfies Contact
}

export function addDbGroupCallContact(data) {
  bfglob.gGroupCallContacts.set(data.rid, data, data.orgId)
  bfglob.emit('add_global_group_call_contacts', data)

  const { addCommonContact } = useCommonContact()
  const contact = createContactFromDbGroupCallContact(data)
  if (contact) {
    addCommonContact(contact)
  }
}

export function updateDbGroupCallContact(data) {
  const oldData = bfglob.gGroupCallContacts.get(data.rid)
  // 删除旧的索引
  bfglob.gGroupCallContacts.deleteIndexByKey(oldData.orgId)
  const t = bfproto.bfdx_proto_msg_T('db_group_call_contacts')
  const d = t.create(data)
  const newData = Object.assign(d, oldData, data)
  bfglob.gGroupCallContacts.set(data.rid, data, data.orgId)
  bfglob.emit('update_global_group_call_contacts', newData)

  const { updateCommonContact } = useCommonContact()
  const contact = createContactFromDbGroupCallContact(data)
  if (contact) {
    updateCommonContact(contact)
  }
}

export function deleteDbGroupCallContact(data) {
  bfglob.gGroupCallContacts.delete(data.rid)
  bfglob.emit('delete_global_group_call_contacts', data)

  const { removeCommonContact } = useCommonContact()
  const contact = createContactFromDbGroupCallContact(data)
  if (contact) {
    removeCommonContact(data.rid)
  }
}

export function addDbSingleCallContact(data) {
  bfglob.gSingleCallContacts.set(data.rid, data, data.deviceId)
  bfglob.emit('add_global_single_call_contacts', data)

  const { addCommonContact } = useCommonContact()
  const contact = createContactFromDbSingleCallContact(data)
  if (contact) {
    addCommonContact(contact)
  }
}

export function updateDbSingleCallContact(data) {
  const oldData = bfglob.gSingleCallContacts.get(data.rid)
  if (!oldData) {
    return
  }
  const t = bfproto.bfdx_proto_msg_T('db_single_call_contacts')
  const d = t.create(data)
  const newData = Object.assign(d, oldData, data)
  bfglob.gSingleCallContacts.set(data.rid, data, data.deviceId)
  bfglob.emit('update_global_single_call_contacts', newData)

  const { updateCommonContact } = useCommonContact()
  const contact = createContactFromDbSingleCallContact(data)
  if (contact) {
    updateCommonContact(contact)
  }
}

export function deleteDbSingleCallContact(data) {
  bfglob.gSingleCallContacts.delete(data.rid)
  bfglob.emit('delete_global_single_call_contacts', data)

  const { removeCommonContact } = useCommonContact()
  removeCommonContact(data.rid)
}

export async function insertDbGroupCallContact2db(orgId: string, sortValue: number) {
  if (bfglob.gSingleCallContacts.get(orgId)) {
    return Promise.reject('Existed')
  }
  const orgItem = bfglob.gorgData.get(orgId)
  if (!orgItem) {
    return Promise.reject('No target')
  }
  const options = {
    decodeMsgType: 'db_group_call_contacts',
  }

  const contact = {
    rid: uuid(),
    userRid: bfglob.userInfo.rid,
    orgId: orgItem.rid,
    sortValue: sortValue,
    creatorRid: bfglob.userInfo.rid,
    createTime: bfTime.nowUtcTime(),
  }

  bfproto
    .sendMessage(dbCmd.DB_GROUP_CALL_CONTACTS_INSERT, contact, 'db_group_call_contacts', getDbSubject(), options)
    .then(() => {
      bfglob.emit('add_global_db_group_call_contacts' satisfies CallContactEvent, contact)
      return Promise.resolve()
    })
    .catch(err => {
      bfglob.console.error('Error adding group contact to DB:', contact, err)
      return Promise.reject(err)
    })
}

export async function insertDbSingleCallContact2db(deviceId: string, sortValue: number) {
  if (bfglob.gSingleCallContacts.get(deviceId)) {
    return Promise.reject('Existed')
  }
  const deviceItem = bfglob.gdevices.get(deviceId)
  if (!deviceItem) {
    return Promise.reject('No target')
  }

  const options = {
    decodeMsgType: 'db_single_call_contacts',
  }

  const contact = {
    rid: uuid(),
    userRid: bfglob.userInfo.rid,
    deviceId: deviceItem.rid,
    sortValue: sortValue,
    creatorRid: bfglob.userInfo.rid,
    createTime: bfTime.nowUtcTime(),
  }

  bfproto
    .sendMessage(dbCmd.DB_SINGLE_CALL_CONTACTS_INSERT, contact, 'db_single_call_contacts', getDbSubject(), options)
    .then(() => {
      bfglob.emit('add_global_db_single_call_contacts' satisfies CallContactEvent, contact)
      return Promise.resolve()
    })
    .catch(err => {
      bfglob.console.error('Error adding single contact to DB:', contact, err)
      return Promise.reject(err)
    })
}

export async function deleteDbGroupCallContact2db(contact) {
  bfproto
    .sendMessage(dbCmd.DB_GROUP_CALL_CONTACTS_DELETE, contact, 'db_group_call_contacts', getDbSubject())
    .then(() => {
      bfglob.emit('delete_global_db_group_call_contacts' satisfies CallContactEvent, contact)
      return Promise.resolve()
    })
    .catch(err => {
      bfglob.console.error('Error deleting group contact to DB:', contact, err)
      return Promise.reject(err)
    })
}

export async function deleteDbSingleCallContact2db(contact) {
  bfproto
    .sendMessage(dbCmd.DB_SINGLE_CALL_CONTACTS_DELETE, contact, 'db_single_call_contacts', getDbSubject())
    .then(() => {
      bfglob.emit('delete_global_db_single_call_contacts' satisfies CallContactEvent, contact)
      return Promise.resolve()
    })
    .catch(err => {
      bfglob.console.error('Error deleting single contact to DB:', contact, err)
      return Promise.reject(err)
    })
}

export async function deleteDbGroupCallContactByOrgId2db(orgId: string) {
  const contact = bfglob.gGroupCallContacts.getDataByIndex(orgId)
  if (!contact) {
    return Promise.reject('No target')
  }
  return deleteDbGroupCallContact2db(contact)
}

export async function deleteDbSingleCallContactByDeviceId2db(deviceId: string) {
  const contact = bfglob.gSingleCallContacts.getDataByIndex(deviceId)
  if (!contact) {
    return Promise.reject('No target')
  }
  return deleteDbSingleCallContact2db(contact)
}

// 公网终端类型
export const NetworkDevices = [DeviceTypes.PocDevice, DeviceTypes.ProchatDevice, DeviceTypes.ProchatGatewayDevice]

export function getSingleContactType(deviceType: number): ContactTerminalType {
  if (NetworkDevices.includes(deviceType)) {
    return 'networkTerminal'
  }

  return 'sdcTerminal'
}

export function getGroupContactType(dmrId: string, orgIsVirtual: number): ContactGroupType | 'fullCallContact' {
  if (dmrId == bfglob.fullCallDmrId) {
    return 'fullCallContact'
  }

  switch (orgIsVirtual) {
    case DbOrgIsVirtual.TaskGroup:
      return 'taskGroup'
    case DbOrgIsVirtual.TempGroup:
    case DbOrgIsVirtual.FastTempGroup:
      return 'tempGroup'
    default:
      return 'group'
  }
}
