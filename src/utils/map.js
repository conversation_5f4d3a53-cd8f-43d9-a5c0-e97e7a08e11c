import { getHumiLabel, getIotCmdLabel, getTempLabel, TemperatureCmd } from '@/utils/iot'
import maplibregl from 'maplibre-gl'
import i18n from '@/modules/i18n'
import { deviceIsLocateFromClassName, deviceIsOnlineFromClassName, get_device_status_className, updateDeviceNodeTitle } from './bftree'
import bfutil, { DefOrgRid, getActiveDevices, getDeviceMapLonLat, getMarkerByIndex, MapMarkerTypes, sortByProps } from './bfutil'
import bfTime, { utcToLocalTimeFormat } from './time'
import router from '@/router'
import { convertPxToRem, calcScaleSize } from '@/utils/setRem'
import openDialog from '@/utils/dialog'
import { setSpeakTarget } from './speak'

// 向 maplibregl.Marker 添加自定义更新地图上 marker 名称方法
maplibregl.Marker.prototype.updateMapName = function (name = '') {
  const nameElem = $(this.getElement()).find('.mapDisplayName')
  if (!nameElem) {
    return this
  }

  $(nameElem).html(name)
  return this
}
// 向 maplibregl.Marker 添加自定义更新 popup 内容方法
maplibregl.Marker.prototype.updatePopup = function (content = '') {
  const popup = this.getPopup()
  if (!popup) {
    bfglob.console.warn('can not found popup')
    return this
  }
  popup.setHTML(content)

  return this
}
// 向 maplibregl.Marker 添加自定义显示popup方法
maplibregl.Marker.prototype.showPopup = function (time = 3000) {
  const popup = this.getPopup()
  if (!popup) {
    bfglob.console.warn('can not found popup')
    return this
  }
  if (!popup.isOpen()) {
    this.togglePopup()
    setTimeout(
      function () {
        this.togglePopup()
      }.bind(this),
      time
    )
  }
  return this
}
// 向 maplibregl.Marker 添加自定义更新 marker 图标方法
maplibregl.Marker.prototype.updateImage = function (
  opts = {
    // 默认参数：type=1为marker数据的类型，如巡查点或地图标记点
    // data为base64格式图片或rgb格式颜色
    // imgOrColorPoint 为地图标记点的颜色点或图标标记点
    type: 2,
    imgOrColorPoint: 0,
    data: '',
    width: 16,
    height: 16,
  }
) {
  const el = this.getElement()
  const $el = $(el)
  const marker_el = $el.find('.map_marker')
  if (!marker_el) {
    bfglob.console.warn('can not found DOM element')
    return this
  }
  // 初始化 marker 的样式
  const marker_el_css = {
    'background-color': 'unset',
    'background-image': 'none',
    'background-size': 'unset',
    'border-radius': '50%',
    width: '16px',
    height: '16px',
  }
  // 判断数据类型和 marker 的颜色类型重置差异的样式
  if (opts.type === 2) {
    // 巡查点
    marker_el_css['background-image'] = 'url(' + opts.data + ')'
    marker_el_css['background-size'] = 'cover'
  } else if (opts.type === 3) {
    // 地图标记点
    if (opts.imgOrColorPoint === 1) {
      // 图片标记点
      marker_el_css['background-image'] = 'url(' + opts.data + ')'
      marker_el_css['background-size'] = '100% 100%'
      marker_el_css['border-radius'] = '4px'
      // marker_el_css.transform = 'unset'
      marker_el_css.width = opts.width + 'px'
      marker_el_css.height = opts.height + 'px'
    } else {
      // 颜色标记点
      marker_el_css['background-color'] = opts.data
    }
  }
  marker_el.css(marker_el_css)

  // 更新 marker 在地图上的名称定位高度
  const mapDisplayName_el = $el.find('.mapDisplayName')
  if (mapDisplayName_el) {
    mapDisplayName_el.css({
      top: opts.height / 2 + 'px',
    })
  }

  return this
}
// 向 maplibregl.Marker 添加自定义隐藏或显示 marker 容器元素方法,true 为隐藏，false 为显示
maplibregl.Marker.prototype.hide = function (bool = true) {
  const el = this.getElement()
  bool ? el.classList.add('hide') : el.classList.remove('hide')
}
// 向 maplibregl.Marker 添加自定义隐藏或显示 marker 图标方法,true 为隐藏，false 为显示
maplibregl.Marker.prototype.hideIcon = function (bool = true) {
  let markerElem = $(this.getElement()).find('.map_marker')
  if (markerElem && markerElem[0]) {
    markerElem = markerElem[0]
    bool ? markerElem.classList.add('hide') : markerElem.classList.remove('hide')
  }
}
// 向 maplibregl.Marker 添加自定义隐藏或显示 marker 名称方法,true 为隐藏，false 为显示
maplibregl.Marker.prototype.hideName = function (bool = true) {
  let nameElem = $(this.getElement()).find('.mapDisplayName')
  if (nameElem && nameElem[0]) {
    nameElem = nameElem[0]
    bool ? nameElem.classList.add('hide') : nameElem.classList.remove('hide')
  }
}
// 更新巡查点marker活动终端数量
maplibregl.Marker.prototype.updateLinePointBadge = function (activeDevices) {
  if (!activeDevices) {
    return
  }
  const markerWrapper = $(this.getElement()).find('.marker-wrapper.lint-point-marker')
  let mapMarker = markerWrapper.find('.map_marker')
  if (mapMarker && mapMarker[0]) {
    mapMarker = mapMarker[0]
    if (activeDevices.size > 0) {
      mapMarker.innerText = activeDevices.size
    }
  }
}

export function showOnlyLocateTerminals(device) {
  // 如果处于通话中，需要判断是否为有效定位
  const cls = get_device_status_className(device)
  let isCallingPosition = false
  if (cls.includes('status_icon_th') && device.av === 1 && bfutil.checkedDeviceLastLonValid(device)) {
    isCallingPosition = true
  }
  if (!isCallingPosition && !deviceIsLocateFromClassName(cls)) {
    deleteOfflineDeviceMarker(device)
    return
  }

  // 获取对讲机marker对象
  const lngLat = [device.lastLon, device.lastLat]
  let marker = bfglob.gdevices.getMarker(device.rid)
  if (!marker) {
    marker = mapMarker({
      type: 1,
      id: device.rid,
      className: cls,
      markerName: device.selfId,
      lngLat,
      data: device,
    })
    if (!marker) {
      return
    }
    bfglob.gdevices.setMarker(device.rid, marker)
  }

  const el = marker.getElement()
  const markerIcon = $(el).find('.map_marker')[0]
  markerIcon.className = 'map_marker ' + cls
  const markerName = $(el).find('.mapDisplayName')[0]
  markerName.innerHTML = device.selfId

  if (verifyLonLat(lngLat)) {
    marker.setLngLat(lngLat)
  }
  marker.addTo(bfglob.map)

  markerIcon.classList.remove('hide')
  markerName.classList.remove('hide')
  el.classList.remove('hide')
}

// 同步对讲机在地图上的 marker 状态
export function updateDeviceMarkerStatus(device) {
  if (!device) {
    return
  }
  // 判断是否只显示在线终端，并更新对应的marker状态
  if (bfglob.userInfo.setting.showOnlyLocateTerminals) {
    showOnlyLocateTerminals(device)
    return
  }

  // 正常更新手台状态
  let cls = get_device_status_className(device)
  if (bfglob.userInfo.setting.showOnlyOnlineTerminals && !deviceIsOnlineFromClassName(cls)) {
    // 手台不在线则删除对应的marker
    deleteOfflineDeviceMarker(device)
    return
  }

  // 判断最后数据时间是否已超过7天，是则不再显示
  const dayTime7 = bfTime.get_current_time_before_one_day_time(bfglob.sysConfig.maxAgeOfDataTime || 10080)
  let lastDataTime = device.lastDataTime
  lastDataTime = bfTime.utcTimeToLocalTime(lastDataTime)
  if (bfTime.comparison_two_time(lastDataTime, dayTime7)) {
    deleteOfflineDeviceMarker(device)
    return
  }

  // 需要显示marker,判断是否需要显示图标
  // 获取对讲机marker对象
  let marker = bfglob.gdevices.getMarker(device.rid)
  if (marker) {
    // 重置marker坐标，校验经纬度是否合法
    const lonLat = [device.lastLon, device.lastLat]
    if (verifyLonLat(lonLat)) {
      marker.setLngLat(lonLat)
    }
  } else {
    if (cls.includes('device_status_none')) {
      cls += ' hide'
    }
    // 添加 devMarker marker
    marker = mapMarker({
      type: 1,
      id: device.rid,
      className: cls,
      markerName: device.selfId,
      lngLat: [device.lastLon, device.lastLat],
      data: device,
    })
    if (!marker) {
      return
    }
    bfglob.gdevices.setMarker(device.rid, marker)
  }

  // 更新popup内容
  const popup = marker.getPopup()
  if (popup.isOpen()) {
    marker.updatePopup(get_popup_content_for_device(device))
  }

  // 更新marker内容
  const el = marker.getElement()
  const markerIcon = $(el).find('.map_marker')[0]
  markerIcon.className = 'map_marker ' + cls
  const markerName = $(el).find('.mapDisplayName')[0]
  markerName.innerHTML = device.selfId

  marker.addTo(bfglob.map)

  // 判断最后数据时间是否在半小时内，超出半小时则只显示名字
  const oneDayTime = bfTime.get_current_time_before_one_day_time(bfglob.sysConfig.maxAgeOfOnline || 30)
  if (bfTime.comparison_two_time(lastDataTime, oneDayTime)) {
    markerIcon.classList.add('hide')
    markerName.classList.remove('hide')
    el.classList.remove('hide')
    return
  }

  // 手台处于在线状态
  markerIcon.classList.remove('hide')
  markerName.classList.remove('hide')
  el.classList.remove('hide')
}

// 单位的地图标记点的RID与单位的RID一致
export function updateOrgMarkerStatus(dbOrg) {
  if (!dbOrg) {
    return
  }
  const marker = bfglob.gmapPoints.getMarker(dbOrg.rid)
  if (!marker) {
    return
  }
  // marker.getElement()?.classList.remove('hide')
  marker.hide(false)
}

export function deleteOfflineDeviceMarker(device) {
  const marker = bfglob.gdevices.getMarker(device.rid)
  if (!marker) {
    return
  }
  marker.remove()
}

export function checkDeviceMarkerDisplayStatus() {
  const devices = bfglob.gdevices.getAll()
  for (const k in devices) {
    const deviceItem = devices[k]
    // 更新手台终端树型结构节点状态
    updateDeviceNodeTitle('bftree', deviceItem)

    // 更新手台地图上的marKer状态
    updateDeviceMarkerStatus(deviceItem)
  }
}

// 右键设备marker事件，显示设备信息和操作按钮
export function deviceRightClickHandler(data) {
  if (Object.prototype.toString.call(data) !== '[object Object]') {
    return
  }

  const marker = bfglob.gdevices.getMarker(data.rid)
  if (!marker) {
    return
  }

  let popup = marker.getPopup()
  if (!popup) {
    popup = new maplibregl.Popup({
      closeButton: false,
      closeOnClick: false,
    })
    popup.setLngLat(marker.getLngLat())
  }

  const content = generateDeviceRightClickHtml(data)
  popup.setHTML(content)
  popup.addTo(marker._map)
}

// 生成简化的设备信息内容
export function get_simplified_device_info(device) {
  if (!device) {
    return ''
  }

  const userName = device.userName || ''
  const deviceTypeName = getDeviceTypeName(device.deviceType)
  const coordinates = `${device.lastLon || 0}, ${device.lastLat || 0}`
  const deviceName = device.selfId || ''
  const deviceId = device.dmrId || ''

  return `
    <div class='popup-body'>
      <p><span class="popup-item-label">${i18n.global.t('dialog.userName')}: </span><span class="popup-item-value">${userName}</span><span style="margin-left: 20px;" class="popup-item-label">${i18n.global.t('dialog.deviceType')}: </span><span class="popup-item-value">${deviceTypeName}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dialog.lngLat')}: </span><span class="popup-item-value">${coordinates}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dialog.deviceName')}: </span><span class="popup-item-value">${deviceName}</span><span style="margin-left: 20px;" class="popup-item-label">${i18n.global.t('dialog.serialNo')}: </span><span class="popup-item-value">${deviceId}</span></p>
    </div>
  `
}

// 获取设备类型名称
function getDeviceTypeName(deviceType) {
  const deviceTypeNames = {
    0: i18n.global.t('dialog.interphone'),
    1: i18n.global.t('dialog.mobile'),
    2: i18n.global.t('dialog.commandAgent'),
    3: i18n.global.t('dialog.telephoneGateway'),
    4: '中继虚拟终端',
    5: '互联网关终端',
    6: i18n.global.t('dialog.analogGatewayTerminal'),
    7: i18n.global.t('dialog.digitalGatewayTerminal'),
    8: '2.4G物联巡查终端',
    9: '传统常规DMR手台',
    10: 'SIP网关终端',
    11: '虚拟集群对讲手台',
    12: 'Mesh网关终端',
    13: 'Mesh终端',
    14: 'Prochat公网终端',
    15: 'Prochat网关终端',
    16: 'SIP电话终端',
    21: '基地台',
    22: i18n.global.t('dialog.mobileDevice'),
    23: 'POC终端',
  }
  return deviceTypeNames[deviceType] || '未知类型'
}

export function generateDeviceRightClickHtml(data) {
  const deviceInfo = get_simplified_device_info(data)
  const markerId = data?.rid || data?.id || ''
  const markerType = MapMarkerTypes.Device

  return `
    <div class="popup-wrapper">
      <span class="popup_title" title="${i18n.global.t('map.deviceInfo')}">${i18n.global.t('map.deviceInfo')}</span>
      <span class="close_icon" onclick="closeMapPopup('${markerId}', ${markerType})" title="${i18n.global.t('map.closePopup')}"></span>
      ${deviceInfo}
    </div>
    <div class='popup-device-actions' data-device-rid='${data.rid}'>
      <div class='device-action-row'>
        <button class='device-action-btn left-btn' data-action='quickCall' title='${i18n.global.t('tree.quickCall')}'>
          ${i18n.global.t('tree.quickCall')}
        </button>
        <button class='device-action-btn right-btn' data-action='telecontrol' title='${i18n.global.t('dialog.turnOn')}/${i18n.global.t('dialog.close')}'>
          ${i18n.global.t('dialog.turnOn')}/${i18n.global.t('dialog.close')}
        </button>
      </div>
      <div class='device-action-row'>
        <button class='device-action-btn left-btn' data-action='locationMonitor' title='${i18n.global.t('dialog.locateCtrl')}'>
          ${i18n.global.t('dialog.locateCtrl')}
        </button>
        <button class='device-action-btn center-btn' data-action='trackMonitor' title='${i18n.global.t('dialog.trailCtrl')}'>
          ${i18n.global.t('dialog.trailCtrl')}
        </button>
        <button class='device-action-btn right-btn' data-action='statusCheck' title='${i18n.global.t('tree.status')}'>
          ${i18n.global.t('tree.status')}
        </button>
      </div>
    </div>
  `
}

// 点击巡查点活动终端徽章事件，显示所有的终端数据
export function linePointBadgeOnClick(data) {
  if (Object.prototype.toString.call(data) !== '[object Object]') {
    return
  }

  const marker = bfglob.glinePoints.getMarker(data.rid)
  if (!marker) {
    return
  }

  let popup = marker.getPopup()
  if (!popup) {
    popup = new maplibregl.Popup({
      closeButton: false,
      closeOnClick: false,
    })
    popup.setLngLat(marker.getLngLat())
  }

  const content = generateActiveDevicesHtml(data)
  popup.setHTML(content)
  popup.addTo(marker._map)
}

export function generateActiveDevicesHtml(data) {
  // 获取活动设备列表
  const activeDevices = []
  for (const rid of data.activeDevices) {
    const device = bfglob.gdevices.get(rid)
    if (!device) {
      continue
    }
    activeDevices.push(device)
  }

  // 按最后RFID时间降序排序
  activeDevices.sort((a, b) => {
    return sortByProps(a, b, { lastRfidTime: 'desc' })
  })

  // 生成设备列表内容
  const deviceListContent = activeDevices
    .map(device => {
      const lastRfidTime = utcToLocalTimeFormat(device.lastRfidTime)
      const userRid = !device.lastRfidPerson || device.lastRfidPerson === DefOrgRid ? device.deviceUser : device.lastRfidPerson
      const userName = bfglob.guserData.getUserNameByKey(userRid)

      return `
        <div class='popup-active-device-item'>
          <p><span class="popup-item-label">${i18n.global.t('msgbox.lastRfidTime')}: </span><span class="popup-item-value">${lastRfidTime}</span></p>
          <p><span class="popup-item-label">${i18n.global.t('dialog.serialNo')}: </span><span class="popup-item-value">${device.selfId}</span></p>
          ${userName ? `<p><span class="popup-item-label">${i18n.global.t('dialog.userName')}: </span><span class="popup-item-value">${userName}</span></p>` : ''}
        </div>
      `
    })
    .join('')

  const title = i18n.global.t('map.linePointInfo')
  const markerId = data?.rid || data?.id || ''
  const markerType = MapMarkerTypes.LinePoint

  return `
    <div class="popup-wrapper">
      <span class="popup_title" title="${title}">${title}</span>
      <span class="close_icon" onclick="closeMapPopup('${markerId}', ${markerType})" title="${i18n.global.t('map.closePopup')}"></span>
      <div class='popup-active-devices-header'>
        <p><span class="popup-item-label">${i18n.global.t('iotDevHistory.activeDeviceCount')}: </span><span class="popup-item-value">${activeDevices.length}</span></p>
      </div>
      <div class='popup-body'>
        <div class='popup-active-device-list'>
          ${deviceListContent}
        </div>
      </div>
      <p class='select_more_linPoint_list popup-footer' onclick='link_select_detailedInspection("${data.rid}")'>
        ${i18n.global.t('dialog.moreLinePointHty')}
      </p>
    </div>
  `
}

export function getLinePointPopupHeader(linePoint) {
  const orgShortName = bfglob.gorgData.getOrgNameByKey(linePoint.orgId)
  return `<p class='popup-header'>${orgShortName} - ${linePoint.pointId} - ${linePoint.pointName}</p>`
}

export function getLinePointPopupFooter(linePoint) {
  return `<span class='select_more_linPoint_list popup-footer'
            onclick='link_select_detailedInspection("${linePoint.rid}")'>
            ${i18n.global.t('dialog.moreLinePointHty')}
          </span>`
}

export function get_popup_content_for_linePoint(linePoint) {
  if (!linePoint) {
    return ''
  }

  // let content = getLinePointPopupHeader(linePoint)
  let content = ''
  const device = bfglob.gdevices.get(linePoint.lastCheckDeviceId)
  const orgShortName = bfglob.gorgData.getOrgNameByKey(linePoint.orgId)
  const lastCheckTime = linePoint.lastCheckTime ? utcToLocalTimeFormat(linePoint.lastCheckTime) : ''

  content += `<div class='last-inspection-info popup-body'>
        <p><span class="popup-item-label">${i18n.global.t('dialog.parentOrgName')}: </span><span class="popup-item-value">${orgShortName}</span></p>
        <p><span class="popup-item-label">${i18n.global.t('dialog.serialNo')}: </span><span class="popup-item-value">${linePoint.pointId}</span></p>
        <p><span class="popup-item-label">${i18n.global.t('dialog.name')}: </span><span class="popup-item-value">${linePoint.pointName}</span></p>`

  if (lastCheckTime) {
    content += `<p><span class="popup-item-label">${i18n.global.t('dialog.lastCheckTime')}:</span><span class="popup-item-value"> ${lastCheckTime}</span></p>`
  }

  if (device) {
    content += `<p><span class="popup-item-label">${i18n.global.t('dialog.lastCheckDevice')}:</span><span class="popup-item-value">${device.orgShortName + ' / ' + device.selfId}</span></p>`
    content += `<p><span class="popup-item-label">${i18n.global.t('dialog.lastCheckUser')}:</span><span class="popup-item-value"> ${device.userName ?? ''}</span></p>`
  }

  content += `</div>`
  content += getLinePointPopupFooter(linePoint)

  return content
}

export function get_popup_content_for_mapPoint(mapPoint) {
  if (!mapPoint) {
    return ''
  }

  return `
    <div class='popup-body'>
      <p><span class="popup-item-label">${i18n.global.t('dialog.parentOrgName')}: </span><span class="popup-item-value">${bfglob.gorgData.getOrgNameByKey(mapPoint.orgId)}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dialog.serialNo')}: </span><span class="popup-item-value">${mapPoint.selfId}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dialog.name')}: </span><span class="popup-item-value">${mapPoint.pointName}</span></p>
    </div>
  `
}

export function calc_device_direction(direction) {
  const degrees = i18n.global.t('map.degrees')
  if (direction === 0 || direction === 360) {
    return i18n.global.t('map.N')
  }
  if (direction < 360 && direction > 315) {
    return i18n.global.t('map.NbyW') + (360 - direction).toFixed(1) + degrees
  }
  if (direction === 315) {
    return i18n.global.t('map.WN')
  }
  if (direction < 315 && direction > 270) {
    return i18n.global.t('map.WbyN') + (direction - 270).toFixed(1) + degrees
  }
  if (direction === 270) {
    return i18n.global.t('map.W')
  }
  if (direction < 270 && direction > 225) {
    return i18n.global.t('map.WbyS') + (direction - 225).toFixed(1) + degrees
  }
  if (direction === 225) {
    return i18n.global.t('map.WS')
  }
  if (direction < 225 && direction > 180) {
    return i18n.global.t('map.SbyW') + (direction - 180).toFixed(1) + degrees
  }
  if (direction === 180) {
    return i18n.global.t('map.S')
  }
  if (direction < 180 && direction > 135) {
    return i18n.global.t('map.SbyE') + (direction - 135).toFixed(1) + degrees
  }
  if (direction === 135) {
    return i18n.global.t('map.ES')
  }
  if (direction < 135 && direction > 90) {
    return i18n.global.t('map.EbyS') + (direction - 90).toFixed(1) + degrees
  }
  if (direction === 90) {
    return i18n.global.t('map.E')
  }
  if (direction < 90 && direction > 45) {
    return i18n.global.t('map.EbyN') + (direction - 45).toFixed(1) + degrees
  }
  if (direction === 45) {
    return i18n.global.t('map.EN')
  }
  if (direction < 45 && direction > 0) {
    return i18n.global.t('map.NbyE') + direction.toFixed(1) + degrees
  }

  return ''
}

export function get_popup_content_for_device(device) {
  if (!device) {
    return ''
  }

  const lastDataTime = bfTime.getLocalTimeString(bfTime.utcTimeToLocalTime(device.lastDataTime))
  const lastGpsTime = bfTime.getLocalTimeString(bfTime.utcTimeToLocalTime(device.lastGpsTime))
  const userName = device.userName || ''

  return `
    <div class='popup-body'>
      <p><span class="popup-item-label">${i18n.global.t('dialog.parentOrgName')}: </span><span class="popup-item-value">${device.orgShortName}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dialog.serialNo')}: </span><span class="popup-item-value">${device.selfId}</span></p>
      ${userName ? `<p><span class="popup-item-label">${i18n.global.t('dialog.userName')}: </span><span class="popup-item-value">${userName}</span></p>` : ''}
      <p><span class="popup-item-label">${i18n.global.t('msgbox.lastData')}: </span><span class="popup-item-value">${lastDataTime}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('msgbox.lastLocate')}: </span><span class="popup-item-value">${lastGpsTime}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dataTable.speed')}: </span><span class="popup-item-value">${device.speed}KM/H</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dataTable.direction')}: </span><span class="popup-item-value">${calc_device_direction(device.direction)}</span></p>
    </div>
  `
}

function getCtrlStats(ctrlStats) {
  switch (ctrlStats) {
    case 1:
      return i18n.global.t('dialog.connect')
    case 3:
      return i18n.global.t('msgbox.deviceFault')
    // case -1:
    // case 0:
    default:
      return i18n.global.t('dialog.disconnect')
  }
}

export function get_popup_content_for_controller(item) {
  if (!item) {
    return ''
  }

  let content = `
    <div class='popup-body'>
      <p><span class="popup-item-label">${i18n.global.t('dialog.parentOrgName')}: </span><span class="popup-item-value">${item.orgShortName}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dialog.serialNo')}: </span><span class="popup-item-value">${item.selfId}</span></p>
  `

  // 只有当有最后数据时间且不是默认值时才显示
  if (item.lastDataTime && item.lastDataTime !== '2000-01-01 00:00:00') {
    content += `<p><span class="popup-item-label">${i18n.global.t('msgbox.lastData')}: </span><span class="popup-item-value">${item.lastDataTime}</span></p>`
  }

  content += `
      <p><span class="popup-item-label">${i18n.global.t('dialog.status')}: </span><span class="popup-item-value">${getCtrlStats(item.ctrlStats)}</span></p>
    </div>
  `

  return content
}

// 主题地图查询点终端点击查询更多数据
function queryMoreIotDeviceHistory(devId) {
  router.push({
    name: 'iotDeviceHistory',
    params: {
      deviceDmrId: devId,
      getMoreIotDeviceHistory: true,
    },
  })
}

if (!window.queryMoreIotDeviceHistory) {
  window.queryMoreIotDeviceHistory = queryMoreIotDeviceHistory
}

export function get_popup_content_for_iot_device(data) {
  if (!data) {
    return ''
  }

  const parentName = bfglob.gorgData.getShortName(data.orgId)
  const lastDataTime = utcToLocalTimeFormat(data.lastDataTime)

  let content = `
    <div class='popup-body'>
      <p><span class="popup-item-label">${i18n.global.t('dialog.parentOrgName')}: </span><span class="popup-item-value">${parentName}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dialog.name')}: </span><span class="popup-item-value">${data.devName}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('iot.devId')}: </span><span class="popup-item-value">${data.devId}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('dialog.lastDataTime')}: </span><span class="popup-item-value">${lastDataTime}</span></p>
      <p><span class="popup-item-label">${i18n.global.t('iot.lastCmd')}: </span><span class="popup-item-value">${getIotCmdLabel(data.lastCmd)}</span></p>
  `

  //  温/湿度指令，添加对应的温度、温度显示
  if (data.lastCmd === TemperatureCmd.Report) {
    if (data.temp) {
      content += `<p><span class="popup-item-label">${i18n.global.t('iot.temperature')}: </span><span class="popup-item-value">${getTempLabel(data.temp)}</span></p>`
    }
    if (data.humi) {
      content += `<p><span class="popup-item-label">${i18n.global.t('iot.humidity')}: </span><span class="popup-item-value">${getHumiLabel(data.humi)}</span></p>`
    }
  }

  content += `
    </div>
    <div class='popup-footer'>
      <span class='select_more_linPoint_list' onclick='queryMoreIotDeviceHistory("${data.devId}")'>
        ${i18n.global.t('dialog.moreLinePointHty')}
      </span>
    </div>
  `

  return content
}

// 关闭弹窗函数
function closeMapPopup(markerId, markerType) {
  let marker = null

  switch (parseInt(markerType)) {
    case 1: // MapMarkerTypes.Device
      marker = bfglob.gdevices.getMarker(markerId)
      break
    case 2: // MapMarkerTypes.LinePoint
      marker = bfglob.glinePoints.getMarker(markerId)
      break
    case 3: // MapMarkerTypes.MapPoint
      marker = bfglob.gmapPoints.getMarker(markerId)
      break
    case 4: // MapMarkerTypes.Controller
      marker = bfglob.gcontrollers.getMarker(markerId)
      break
    case 5: // MapMarkerTypes.IotDevice
      marker = bfglob.giotDevices?.getMarker(markerId)
      break
  }

  if (marker && marker.getPopup() && marker.getPopup().isOpen()) {
    marker.togglePopup()
  }
}

// 将关闭函数添加到全局，以便在 HTML 中调用
if (!window.closeMapPopup) {
  window.closeMapPopup = closeMapPopup
}

export function getPopupHtml(data, type) {
  let content = ''
  let title = ''
  switch (type) {
    case MapMarkerTypes.LinePoint:
      // 巡查点类型
      content = get_popup_content_for_linePoint(data)
      title = i18n.global.t('map.linePointInfo')
      break
    case MapMarkerTypes.MapPoint:
      // 地图标记点类型
      content = get_popup_content_for_mapPoint(data)
      title = i18n.global.t('map.mapPointInfo')
      break
    case MapMarkerTypes.Controller:
      // 控制器类型
      content = get_popup_content_for_controller(data)
      title = i18n.global.t('map.controllerInfo')
      break
    case MapMarkerTypes.IotDevice:
      content = get_popup_content_for_iot_device(data)
      title = i18n.global.t('map.iotDeviceInfo')
      break
    default:
      // 默认为对讲机类型
      content = get_popup_content_for_device(data)
      title = i18n.global.t('map.deviceInfo')
  }

  // 添加关闭按钮的点击事件，传入数据的 rid 和类型
  const markerId = data?.rid || data?.id || ''
  return `<div class="popup-wrapper">
    <span class="popup_title" title="${title}">${title}</span>
    <span class="close_icon" onclick="closeMapPopup('${markerId}', ${type})" title="${i18n.global.t('map.closePopup')}"></span>
    ${content}
  </div>`
}

function getMarkerIfExist(type, id) {
  switch (type) {
    case 1:
      return bfglob.gdevices.getMarker(id)
    case 2:
      return bfglob.glinePoints.getMarker(id)
    case 3:
      return bfglob.gmapPoints.getMarker(id)
    case 4:
      return bfglob.gcontrollers.getMarker(id)
    default:
      return null
  }
}

// type类型说明：1为对讲机，2为巡查点，3为地图标记点，4为控制器，默认为对讲机marker
function getGroupName(type) {
  switch (type) {
    case MapMarkerTypes.LinePoint:
      return 'linePoint'
    case MapMarkerTypes.MapPoint:
      return 'mapPoint'
    case MapMarkerTypes.Controller:
      return 'ctrlMarker'
    case MapMarkerTypes.IotDevice:
      return 'iotMarker'
    default:
      return 'devMarker'
  }
}

function isBetweenRange(val, range) {
  if (!Array.isArray(range)) {
    return false
  }
  const [min, max] = range
  return val >= min && val <= max
}

export function verifyLonLat(lonLat, Match_lngLats) {
  if (!Array.isArray(lonLat)) {
    return false
  }

  const [lon, lat] = lonLat
  let LonRange = [-180, 180]
  let LatRange = [-90, 90]

  if (Match_lngLats) {
    LonRange = [Match_lngLats.minLon ?? -180, Match_lngLats.maxLon ?? 180]
    LatRange = [Match_lngLats.minLat ?? -90, Match_lngLats.maxLat ?? 90]
  }

  return !!(isBetweenRange(lon, LonRange) && isBetweenRange(lat, LatRange))
}

export function createPopup(opts) {
  const popup = new maplibregl.Popup({
    closeButton: opts.closeButton,
    closeOnClick: opts.closeOnClick,
  })
    .setHTML(opts.content)
    .addTo(bfglob.map)

  if (opts.lngLat) {
    popup.setLngLat(opts.lngLat)
  }

  return popup
}

export function createMarker(opts) {
  const marker = opts.offset ? new maplibregl.Marker({ element: opts.el, offset: opts.offset }) : new maplibregl.Marker({ element: opts.el })

  if (opts.popup) {
    marker.setPopup(opts.popup)
  }
  marker.setLngLat(opts.lngLat).addTo(bfglob.map)

  return marker
}

export function createMarkerContainer(opts, children) {
  const container = document.createElement('div')
  container.className = 'markerContainer ' + opts.groupName
  container.id = opts.id
  children.forEach(child => {
    container.appendChild(child)
  })

  return container
}

export function createNameElem(opts) {
  const span = document.createElement('span')
  span.innerHTML = opts.markerName || ''
  span.className = 'mapDisplayName'

  if (opts.type === MapMarkerTypes.LinePoint) {
    if (bfglob.userInfo.setting.showLinePointName) {
      span.className = 'mapDisplayName'
    } else {
      span.className = 'mapDisplayName hide'
    }
  }
  // if (opts.type === MapMarkerTypes.MapPoint && opts.data.imgOrColorPoint === 1) {
  //   span.style.top = opts.data.markerHeight / 2 + 'px'
  // }

  return span
}

export function createMarkerElem(opts) {
  const el = document.createElement('div')
  el.className = 'map_marker ' + opts.className
  if (opts.type === MapMarkerTypes.MapPoint && opts.data.imgOrColorPoint === 1) {
    el.style.borderRadius = '4px'
    el.style.width = opts.data.markerWidth + 'px'
    el.style.height = opts.data.markerHeight + 'px'
  }
  if (!opts.icon) {
    // el.style.backgroundColor = "#f00";
  } else if (opts.icon[0] === '#' || opts.icon.indexOf('rgb') > -1) {
    el.style.backgroundColor = opts.icon || '#f00'
  } else if (opts.icon.endsWith('.svg')) {
    // 处理SVG图标
    el.style.backgroundImage = 'url(' + opts.icon + ')'
    el.style.backgroundSize = 'contain'
    el.style.backgroundRepeat = 'no-repeat'
    el.style.backgroundPosition = 'center'
    // 为SVG图标设置合适的尺寸
    if (opts.type === MapMarkerTypes.Controller || opts.type === MapMarkerTypes.IotDevice) {
      el.style.width = '28px'
      el.style.height = '34px'
      el.style.borderRadius = '0'
    }
  } else {
    el.style.backgroundImage = 'url(' + opts.icon + ')'
    el.style.backgroundSize = '100% 100%'
  }

  // 设备类型，绑定右键事件弹窗
  if (opts.type === MapMarkerTypes.Device) {
    el.oncontextmenu = e => {
      e.preventDefault()
      e.stopPropagation()
      deviceRightClickHandler(opts.data)
    }
  }

  // 巡查点，绑定一个右键点击事件弹窗
  if (opts.type === MapMarkerTypes.LinePoint) {
    const wrapper = document.createElement('div')
    wrapper.className = 'marker-wrapper lint-point-marker'
    wrapper.oncontextmenu = e => {
      e.preventDefault()
      e.stopPropagation()
      linePointBadgeOnClick(opts.data)
    }

    el.style.fontWeight = 'bold'
    el.style.color = '#fff'
    el.style.lineHeight = convertPxToRem(calcScaleSize(24)) + 'rem'

    // 给巡查点绑定上活动设备记录数量
    const linePoint = opts.data
    bfutil.getRfidDevices(linePoint.pointRfid).forEach(device => {
      addLinePointActiveDevice(linePoint.pointRfid, device.rid)
    })
    const activeDevices = linePoint.activeDevices
    if (activeDevices) {
      el.innerText = activeDevices.size
    }
    wrapper.appendChild(el)

    return wrapper
  }
  return el
}

export function mapMarker(optsObj) {
  // 判断 marker 是否已经存在，如存在则返回该 marker
  const oldMarker = getMarkerIfExist(optsObj.type, optsObj.id)
  if (oldMarker) {
    return oldMarker
  }

  // 没有地图对象，返回null
  if (!bfglob.map) {
    return null
  }

  // 设置默认的属性配置
  const opts = {
    type: optsObj.type || MapMarkerTypes.Device,
    id: optsObj.id ? 'x' + optsObj.id : 'x' + new Date().getTime(),
    className: optsObj.className || '',
    markerName: optsObj.markerName || '',
    icon: optsObj.icon || '',
    lngLat: optsObj.lngLat || bfglob.mapConfig.mapCenter || [0, 0],
    showClosePopupBtn: optsObj.showClosePopupBtn || false,
    offset: { offset: [-8, -8] },
    groupName: getGroupName(optsObj.type),
    data: optsObj.data || undefined,
  }

  // 判断经纬度是否有效，无效则退出
  if (!verifyLonLat(opts.lngLat)) {
    return null
  }

  if (opts.type === MapMarkerTypes.MapPoint) {
    opts.offset = {
      offset: [-parseInt(opts.data.markerWidth) / 2, -parseInt(opts.data.markerHeight) / 2],
    }
  }

  // 创建新的 marker 元素
  const markerElem = createMarkerElem(opts)
  // 创建 marker 底部的名称容器
  const nameElem = createNameElem(opts)
  markerElem.appendChild(nameElem)
  // 创建 marker 容器
  const markerContainer = createMarkerContainer(opts, [markerElem])
  if (opts.type === MapMarkerTypes.LinePoint || opts.type === MapMarkerTypes.MapPoint) {
    if (bfglob.map.getZoom() < opts.startShowLevel) {
      markerContainer.className += ' hide'
      // markerContainer.style.display = 'none'
      // } else {
      //   markerContainer.style.display = 'block'
    }
  }

  // 设置 marker popup ,并将 marker 添加到地图
  const point_popup = createPopup({
    closeButton: opts.showClosePopupBtn,
    closeOnClick: true,
    content: getPopupHtml(opts.data, opts.type),
  })

  return createMarker({
    el: markerContainer,
    // offset: opts.offset,
    lngLat: opts.lngLat,
    popup: point_popup,
  })
}

export function updateLinePointActiveDevicesPopup(rfidId) {
  const marker = getMarkerByIndex(rfidId)
  if (!marker) {
    return
  }
  const popup = marker.getPopup()
  if (!popup || !popup.isOpen()) {
    return
  }

  // 在显示popup中，更新对应的popup内容
  const content = popup._content
  if (!content) {
    return
  }

  const linePoint = bfglob.glinePoints.getDataByIndex(rfidId)
  // 判断当前显示的是巡查点最后打卡信息，还是显示活动终端的数量信息
  if (content.innerHTML.includes('popup-active-device-list')) {
    // 显示活动终端数据
    if (linePoint.activeDevices.size > 0) {
      const content = generateActiveDevicesHtml(linePoint)
      popup.setHTML(content)
      popup.addTo(marker._map)
    } else {
      // 当前巡查点没有活动的终端，则关闭popup
      marker.togglePopup()
    }
    return
  }

  if (content.innerHTML.includes('last-inspection-info')) {
    // 显示巡查点最后打卡信息
    popup.setHTML(get_popup_content_for_linePoint(linePoint))
    popup.addTo(marker._map)
    return
  }

  // 正在显示巡查提示信息，则关闭popup
  marker.togglePopup()
}

export function reduceLinePointActiveDevice(rfidId, deviceRid) {
  const activeDevices = getActiveDevices(rfidId)
  if (!activeDevices) {
    return
  }

  activeDevices.delete(deviceRid)
  updateLinePointActiveDevicesPopup(rfidId)
  const marker = getMarkerByIndex(rfidId)
  if (marker) {
    marker.updateLinePointBadge(activeDevices)
  }
}

export function addLinePointActiveDevice(rfidId, deviceRid) {
  const activeDevices = getActiveDevices(rfidId)
  if (!activeDevices) {
    return
  }

  activeDevices.add(deviceRid)
  const marker = getMarkerByIndex(rfidId)
  if (marker) {
    marker.updateLinePointBadge(activeDevices)
  }
}

export function setupLinePointActiveDevice(rfid, device) {
  // 前后两打卡均为同一个巡查点，则结束更新巡查点终端数量
  if (device.lastRfid === rfid.rfidId) {
    return
  }

  const prevLastRfid = device.lastRfid
  device.lastRfid = rfid.rfidId
  device.lastRfidTime = rfid.readTime

  // 将设备从上个巡查点中移除
  reduceLinePointActiveDevice(prevLastRfid, device.rid)
  // 将设备更新到新的巡查点上
  addLinePointActiveDevice(rfid.rfidId, device.rid)
}

const module = {
  updateDeviceMarkerStatus,
  addLinePointActiveDevice,
  setupLinePointActiveDevice,
  deleteOfflineDeviceMarker,
  checkDeviceMarkerDisplayStatus,
  createPopup,
  createMarker,
  // 创建并返回一个marker
  mapMarker,
  // 更新 popup 内容
  calc_device_direction,
  get_popup_content_for_device,
  get_popup_content_for_mapPoint,
  get_popup_content_for_linePoint,
  get_popup_content_for_controller,
  getPopupHtml,
  // 主页地图巡查点点击更多巡查历史跳转到巡查页面
  link_select_detailedInspection(rid) {
    router.push({
      name: 'InspectionHistory',
      params: {
        pointRid: rid,
        getMoreInspectionHistory: true,
      },
    })
  },
  show_or_hide_linePointMarker() {
    if (bfglob.userInfo.setting.showLintPointMarker) {
      $('.linePoint').show()
    } else {
      $('.linePoint').hide()
    }
  },
  show_or_hide_mapPointMarker() {
    if (bfglob.userInfo.setting.showMapPointMarker) {
      $('.mapPoint').show()
    } else {
      $('.mapPoint').hide()
    }
  },
  show_or_hide_ctrlMarker() {
    if (bfglob.userInfo.setting.showCtrlMarker) {
      $('.ctrlMarker').show()
    } else {
      $('.ctrlMarker').hide()
    }
  },
  show_or_hide_mapLinePointNames() {
    var mapDisplayNames = $('#bfmap .linePoint .mapDisplayName')
    if (mapDisplayNames.length === 0) {
      return
    }

    if (bfglob.userInfo.setting.showLinePointName) {
      for (let i = 0; i < mapDisplayNames.length; i++) {
        mapDisplayNames[i].className = 'mapDisplayName'
      }
    } else {
      for (let i = 0; i < mapDisplayNames.length; i++) {
        mapDisplayNames[i].className = 'mapDisplayName hide'
      }
    }
  },
  // 验证经纬度是否合法
  verifyLonLat,
  verifyLevel(level) {
    if (level < 1.5) {
      level = 1.5
    } else if (level > 18) {
      level = 18
    }
    return level
  },
  setMapZoom(level) {
    bfglob.map && bfglob.map.setZoom(this.verifyLevel(level))
  },
  setMapCenter(lonLat) {
    this.verifyLonLat(lonLat) && bfglob.map && bfglob.map.setCenter(lonLat)
  },
  updatePopupContent(data, type) {
    if (!data) {
      return
    }
    const marker = bfutil.getMarkerByKey(data.rid, type)
    marker && marker.updatePopup(this.getPopupHtml(data, type)).showPopup()
  },
  // type 1：终端（对讲机），2：巡查点，3：地图标记点，4：设备（控制器）
  dbclickJumpToMarker(data, type) {
    if (!data) {
      return
    }

    let lonLat = [data.lon, data.lat]
    if (type === 1) {
      lonLat = getDeviceMapLonLat(data)
    }

    // 如果标记点、巡查点等显示级别大于当前地图级别则将地图放大至显示标记点级别
    let showLevel
    if (typeof data.startShowLevel !== 'undefined') {
      showLevel = data.startShowLevel
    }
    mapFlyTo(lonLat, showLevel)

    // 更新地图 marker popup 内容
    this.updatePopupContent(data, type)
  },
  close_mapLaye_btn: null,
  create_layers_of_bcxx_result(displayBcxx) {
    const currentRouteName = router.currentRoute.name
    const rectangleId = 'bcxx_result_rectangle'
    var close_result_layer = () => {
      if (bfglob.map.getLayer(rectangleId)) {
        bfglob.map.removeLayer(rectangleId)
      }
      if (bfglob.map.getSource(rectangleId)) {
        bfglob.map.removeSource(rectangleId)
      }
      router.push({ name: currentRouteName })
    }
    try {
      var top_lf = [displayBcxx.minLon, displayBcxx.maxLat] // 西北坐标
      var bottom_rt = [displayBcxx.maxLon, displayBcxx.minLat] // 东南坐标
      var top_rt = [displayBcxx.maxLon, displayBcxx.maxLat] // 东北坐标，最大经纬度
      var bottom_lf = [displayBcxx.minLon, displayBcxx.minLat] // 西南坐标，最小经纬度
      const coordinates = [top_lf, top_rt, bottom_rt, bottom_lf, top_lf]

      const rectangleSource = bfglob.map.getSource(rectangleId)
      if (rectangleSource) {
        rectangleSource.setData({
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [coordinates],
          },
        })
      } else {
        bfglob.map.addSource(rectangleId, {
          type: 'geojson',
          data: {
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'Polygon',
              coordinates: [coordinates],
            },
          },
        })
      }
      if (!bfglob.map.getLayer(rectangleId)) {
        bfglob.map.addLayer({
          id: rectangleId,
          type: 'fill',
          source: rectangleId,
          paint: {
            'fill-color': '#20a0ff',
            'fill-opacity': 0.2,
          },
        })
      }

      // 创建关闭按钮marker
      if (this.close_mapLaye_btn) {
        this.close_mapLaye_btn.setLngLat(top_rt).addTo(bfglob.map)
      } else {
        var c_div = document.createElement('div')
        c_div.innerHTML = '&times;'
        c_div.className = 'range_close_btn'
        c_div.onclick = () => {
          setTimeout(() => {
            if (this.close_mapLaye_btn !== null) {
              this.close_mapLaye_btn.remove()
              this.close_mapLaye_btn = null
            }
            close_result_layer()
          }, 50)
        }
        this.close_mapLaye_btn = this.createMarker({
          el: c_div,
          lngLat: top_rt,
        })
      }

      // 计算围栏的中心坐标，重置地图中心点
      var c_lon = displayBcxx.maxLon - (displayBcxx.maxLon - displayBcxx.minLon) / 2
      var c_lat = displayBcxx.maxLat - (displayBcxx.maxLat - displayBcxx.minLat) / 2
      var c_lngLat = [c_lon, c_lat]
      bfglob.map.setCenter(c_lngLat)
      router.push({ name: 'main' })
    } catch (_e) {
      close_result_layer()
    }
  },
  setPaintProperty(layerId, property, opacity) {
    bfglob.map.getLayer(layerId) && bfglob.map.setPaintProperty(layerId, property, opacity)
  },
  add3dLayer(opts) {
    const source = bfglob.map.getSource(opts.id)
    if (source) {
      source.setData(opts.source)
    } else {
      bfglob.map.addSource(opts.id, {
        type: 'geojson',
        data: opts.source,
      })
    }

    const layer = bfglob.map.getLayer(opts.id)
    if (!layer) {
      bfglob.map.addLayer({
        id: opts.id,
        type: opts.type,
        source: opts.id,
        maxzoom: 24,
        minzoom: 18,
        paint: opts.paint || {},
        layout: opts.layout || {},
      })
    }
  },
}

export default module

if (!window.link_select_detailedInspection) {
  window.link_select_detailedInspection = module.link_select_detailedInspection.bind(module)
}

export function mapFlyTo(center, zoom = 1) {
  if (!bfglob.map || !verifyLonLat(center)) {
    return
  }

  const options = {
    center,
    zoom: Math.max(zoom, bfglob.map.getZoom(), bfglob.mapConfig.defaultZoom || 15),
    essential: true, // this animation is considered essential with respect to prefers-reduced-motion
  }
  bfglob.map.flyTo(options)
}

export function showIotMarker() {
  if (bfglob.userInfo.setting.showIotDeviceMarkers) {
    $('.iotMarker').show()
  } else {
    $('.iotMarker').hide()
  }
}

export function deferCloseMarkerPopup(marker, popup) {
  setTimeout(function () {
    marker.setPopup(popup)
    if (popup.isOpen()) {
      marker.togglePopup()
    }
  }, 8 * 1000)
}

// 所有支持的地图名称
export const SupportMapName = {
  google: 'google',
  tianditu: 'tianditu',
}

// 天地图地图样式配置
export const MapStyleName = {
  streets: 'streets',
  satellite: 'satellite',
}

// export const GoogleMapTypes = {
//   streets: 'webrd',
//   satellite: 'webst',
//   satelliteSign: 'wprd',
// }

export const TiandituMapTypes = {
  streets: 'vec_w',
  satellite: 'img_w',
  'streets_zh-CN': 'cva_w',
  'satellite_zh-CN': 'cia_w',
  'streets_en-US': 'eva_w',
  'satellite_en-US': 'eia_w',
}

export function getTiandituMapLang() {
  // if (i18n.global.locale === SupportedLang.zhCN) return 'zh-CN'
  // return 'en-US'
  //todo 天地图暂时无法请求英文注记
  return 'zh-CN'
}

export function getMapMinzoom() {
  if (typeof bfglob.mapConfig.tilesMinzoom === 'number' && bfglob.mapConfig.tilesMinzoom >= 0 && bfglob.mapConfig.tilesMinzoom < 18) {
    return bfglob.mapConfig.tilesMinzoom
  }

  return 1
}

export function getMapMaxzoom() {
  if (typeof bfglob.mapConfig.tilesMaxzoom === 'number' && bfglob.mapConfig.tilesMaxzoom > 0 && bfglob.mapConfig.tilesMaxzoom <= 18) {
    return bfglob.mapConfig.tilesMaxzoom
  }

  return 18
}

// 现在由bfmap请求和缓存地图数据
/*export function getTiandituStyle(mapTypeName) {
  // const tiandituKey = '93c5c850399ca62f0f16ba4f95f7688e'
  const tiandituKey = bfglob.mapConfig.token

  if (!MapStyleName[mapTypeName]) {
    mapTypeName = MapStyleName.streets
  }

  // 使用天地图 `https://t0.tianditu.gov.cn/DataServer?tk=${tiandituKey}`
  // t0~t7
  const tx = Math.floor(Math.random() * 7)
  const baseUrl = `https://t${tx}.tianditu.gov.cn/DataServer?tk=${tiandituKey}`
  const params = '&x={x}&y={y}&l={z}'
  const mapType = TiandituMapTypes[mapTypeName]
  const langTypeKey = `${mapTypeName}_${getTiandituMapLang(i18n.locale)}`
  //@ts-ignore
  const langType = TiandituMapTypes[langTypeKey] || TiandituMapTypes['streets_en-US']

  const path = `${window.location.origin}${BASE_URL}localMapbox`
  const glyphs = `${path}/glyphs/{fontstack}/{range}.pbf`
  const sprite = `${path}/sprite/sprite`

  const tileSize = 256
  const minzoom = getMapMinzoom()
  const maxzoom = getMapMaxzoom()

  const style = {
    version: 8,
    glyphs: glyphs,
    sprite: sprite,
    name: mapTypeName,
    sources: {
      [mapTypeName]: {
        type: 'raster',
        tiles: [`${baseUrl}&T=${mapType}${params}`],
        tileSize,
        minzoom,
        maxzoom,
      },
      [langTypeKey]: {
        type: 'raster',
        tiles: [`${baseUrl}&T=${langType}${params}`],
        tileSize,
        minzoom,
        maxzoom,
      },
    },
    layers: [
      {
        id: mapTypeName,
        type: 'raster',
        source: mapTypeName,
      },
      {
        id: langTypeKey,
        type: 'raster',
        source: langTypeKey,
      },
    ],
  }
  return style
}*/

// 开启坐标选择控件
export class SelectLngLatControl {
  name = 'select-lnglat'
  selecting = false
  pointColor = '#20a0ff'
  pointId = 'bcxx-point'

  get startPointId() {
    return this.pointId + '-start'
  }

  get endPointId() {
    return this.pointId + '-end'
  }

  rectangleId = 'bcxx-rectangle'
  rectangleOutlineId = 'bcxx-rectangle-outline'
  layersIsCreated = false
  isMovePoint = false
  points = [
    {
      type: 'Feature',
      properties: {
        id: this.startPointId,
      },
      geometry: {
        type: 'Point',
        coordinates: [],
      },
    },
    {
      type: 'Feature',
      properties: {
        id: this.endPointId,
      },
      geometry: {
        type: 'Point',
        coordinates: [],
      },
    },
  ]

  lngLatRange = {
    minLon: 0,
    maxLon: 0,
    minLat: 0,
    maxLat: 0,
  }

  /**
   * @type {object}
   * @description 地图控件配置
   * @property {object} vnode - vue的实例对象
   */
  options = {}

  button = undefined
  cancelWatchLocale = () => true

  constructor(options) {
    this.options = Object.assign(this.options, options)
    this.container = document.createElement('div')
    this.container.className = `maplibregl-ctrl maplibregl-ctrl-group ${this.name}-control`

    bfglob.on('map-close', mapId => {
      if (mapId !== this.map?.mapId) {
        return
      }

      this.onMapClose()
    })

    // 修改地图事件this指向
    this.onMouseDown = this.onMouseDown.bind(this)
    this.onMouseMove = this.onMouseMove.bind(this)
    this.onRectangleClick = this.onRectangleClick.bind(this)
    this.onRectangleMouseEnter = this.onRectangleMouseEnter.bind(this)
    this.onMouseLeave = this.onMouseLeave.bind(this)
    this.onPointMouseEnter = this.onPointMouseEnter.bind(this)
    this.onPointMouseDown = this.onPointMouseDown.bind(this)
    this.onPointMouseMove = this.onPointMouseMove.bind(this)
  }

  onRectangleMouseEnter(_e) {
    this.map.getCanvas().style.cursor = 'pointer'
  }

  onMouseLeave(_e) {
    this.map.getCanvas().style.cursor = 'crosshair'
  }

  onPointMouseEnter() {
    this.map.getCanvas().style.cursor = 'move'
  }

  // 解绑地图监听的事件
  offEvent(map) {
    map.off('click', this.onRectangleClick)
    map.off('mousedown', this.onMouseDown)
    map.off('mousemove', this.onMouseMove)
    map.off('mouseenter', this.rectangleId, this.onRectangleMouseEnter)
    map.off('mouseleave', this.rectangleId, this.onMouseLeave)
    map.off('mouseenter', this.pointId, this.onPointMouseEnter)
    map.off('mouseleave', this.pointId, this.onMouseLeave)
    map.off('mousedown', this.pointId, this.onPointMouseDown)
  }

  // 清除所有图层数据
  removeLayers(map) {
    if (map.getLayer(this.pointId)) {
      map.removeLayer(this.pointId)
    }
    if (map.getSource(this.pointId)) {
      map.removeSource(this.pointId)
    }

    if (map.getLayer(this.rectangleId)) {
      map.removeLayer(this.rectangleId)
    }
    if (map.getSource(this.rectangleId)) {
      map.removeSource(this.rectangleId)
    }

    if (map.getLayer(this.rectangleOutlineId)) {
      map.removeLayer(this.rectangleOutlineId)
    }
    if (map.getSource(this.rectangleOutlineId)) {
      map.removeSource(this.rectangleOutlineId)
    }

    this.layersIsCreated = false
    this.isMovePoint = false
  }

  // 创建图层
  createSourceLayers(map) {
    if (this.layersIsCreated) {
      return
    }

    map.addSource(this.rectangleId, {
      type: 'geojson',
      data: {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [],
        },
      },
    })
    map.addLayer({
      id: this.rectangleId,
      type: 'fill',
      source: this.rectangleId,
      paint: {
        'fill-color': this.pointColor,
        'fill-opacity': 0.2,
      },
    })
    map.addSource(this.rectangleOutlineId, {
      type: 'geojson',
      data: {
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: [],
        },
      },
    })
    map.addLayer({
      id: this.rectangleOutlineId,
      type: 'line',
      source: this.rectangleOutlineId,
      paint: {
        'line-color': this.pointColor,
        'line-opacity': 0.3,
        'line-width': 1,
      },
    })
    map.addSource(this.pointId, {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: this.points,
      },
    })
    map.addLayer({
      id: this.pointId,
      type: 'circle',
      source: this.pointId,
      paint: {
        'circle-radius': 6,
        'circle-color': this.pointColor,
      },
    })

    // 标记图层创建完成
    this.layersIsCreated = true
    // 进入/退出矩形范围,修改鼠标样式
    map.on('mouseenter', this.rectangleId, this.onRectangleMouseEnter)
    map.on('mouseleave', this.rectangleId, this.onMouseLeave)
    // 进入/退出点范围,修改鼠标样式
    map.on('mouseenter', this.pointId, this.onPointMouseEnter)
    map.on('mouseleave', this.pointId, this.onMouseLeave)
  }

  // 更新图层
  refreshLayers() {
    // 坐标点图层
    const pointSource = this.map.getSource(this.pointId)
    if (pointSource) {
      pointSource.setData({
        type: 'FeatureCollection',
        features: this.points,
      })
    }

    // 更新矩形的坐标点集
    const topLeft = [...this.points[0].geometry.coordinates]
    const topRight = [this.points[1].geometry.coordinates[0], this.points[0].geometry.coordinates[1]]
    const bottomLeft = [this.points[0].geometry.coordinates[0], this.points[1].geometry.coordinates[1]]
    const bottomRight = [...this.points[1].geometry.coordinates]
    const coordinates = [topLeft, topRight, bottomRight, bottomLeft, topLeft]

    // 计算最大和最小经纬度
    this.lngLatRange.minLon = Math.min(topLeft[0], topRight[0], bottomLeft[0], bottomRight[0])
    this.lngLatRange.maxLon = Math.max(topLeft[0], topRight[0], bottomLeft[0], bottomRight[0])
    this.lngLatRange.minLat = Math.min(topLeft[1], topRight[1], bottomLeft[1], bottomRight[1])
    this.lngLatRange.maxLat = Math.max(topLeft[1], topRight[1], bottomLeft[1], bottomRight[1])

    // 更新矩形和边框线图层
    const rectangleSource = this.map.getSource(this.rectangleId)
    if (rectangleSource) {
      rectangleSource.setData({
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [coordinates],
        },
      })
    }
    const rectangleOutlineSource = this.map.getSource(this.rectangleOutlineId)
    if (rectangleOutlineSource) {
      rectangleOutlineSource.setData({
        type: 'Feature',
        geometry: {
          type: 'LineString',
          coordinates: [...coordinates],
        },
      })
    }
  }

  // 坐标点移动,更新图层
  onPointMouseMove(evt) {
    if (!this.pointFeatures) {
      return
    }

    // 通过features的id属性找到对应的点的数据源,更新数据源坐标
    const pointPropId = this.pointFeatures[0].properties.id
    if (pointPropId === this.startPointId) {
      this.points[0].geometry.coordinates = [evt.lngLat.lng, evt.lngLat.lat]
    } else {
      this.points[1].geometry.coordinates = [evt.lngLat.lng, evt.lngLat.lat]
    }

    // 更新坐标范围所有图层
    this.refreshLayers()
  }

  onPointMouseDown(evt) {
    this.isMovePoint = true
    this.pointFeatures = evt.features
    this.map.on('mousemove', this.onPointMouseMove)
    this.map.once('mouseup', () => {
      this.map.off('mousemove', this.onPointMouseMove)
      this.map.getCanvas().style.cursor = 'crosshair'
      this.isMovePoint = false
    })
  }

  // 鼠标移动,更新图层
  onMouseMove(evt) {
    // 第二个点
    this.points[1].geometry.coordinates = [evt.lngLat.lng, evt.lngLat.lat]
    this.refreshLayers()
  }

  // 监听地图鼠标按下和移动事件,获取两个点坐标
  onMouseDown(evt) {
    if (this.isMovePoint) return
    // 第一个点
    this.points[0].geometry.coordinates = [evt.lngLat.lng, evt.lngLat.lat]
    // 创建图层
    this.createSourceLayers(this.map)

    // Mouse events
    this.map.on('mousemove', this.onMouseMove)
    this.map.once('mouseup', () => {
      this.map.off('mousemove', this.onMouseMove)
    })
  }

  // 需要重写该方法,以便处理结果
  select() {}

  // 点击矩形范围内,确定选择坐标范围
  onRectangleClick(_evt) {
    this.select(this.lngLatRange)
    this.selectLngLatEnd(this.map)
  }

  enterSelectLngLat(map) {
    map.dragPan.disable()
    map.getCanvas().style.cursor = 'crosshair'

    map.on('click', this.rectangleId, this.onRectangleClick)
    map.on('mousedown', this.pointId, this.onPointMouseDown)
    map.on('mousedown', this.onMouseDown)
  }

  exitSelectLngLat(map) {
    this.offEvent(map)
    this.removeLayers(map)
    map.dragPan.enable()
    map.getCanvas().style.cursor = ''
  }

  selectLngLatEnd(_map) {
    this.exitSelectLngLat(this.map)
    this.selecting = false
    this.button.classList.remove('text-blue-600')
  }

  enable(status = true) {
    this.container.style.display = status ? 'block' : 'none'
  }

  onMapClose() {
    if (!this.map) {
      return
    }

    this.selectLngLatEnd(this.map)
  }

  onAdd(map) {
    this.map = map
    this.container.textContent = ''
    this.button = document.createElement('button')
    this.button.className = `maplibregl-ctrl-${this.name} select-coordinates`
    this.button.title = this.button.ariaLabel = i18n.global.t('map.selectCoordinates')

    this.container.onclick = () => {
      if (this.selecting) {
        this.exitSelectLngLat(map)
      } else {
        this.enterSelectLngLat(map)
      }
      this.button.classList.toggle('text-blue-600')
      this.selecting = !this.selecting
    }
    this.container.appendChild(this.button)

    this.cancelWatchLocale = i18n.vm?.$watch('locale', _locale => {
      this.button.title = this.button.ariaLabel = i18n.global.t('map.selectCoordinates')
    })

    return this.container
  }

  onRemove() {
    this.container?.parentNode?.removeChild(this.container)
    this.exitSelectLngLat()
    this.map = undefined
    this.button = undefined
    this.container = undefined
    this.cancelWatchLocale()
  }
}

// 全局canvas 优先使用OffsetScreenCanvas
const hasOffscreenCanvas = typeof OffscreenCanvas !== 'undefined'

function createCanvas() {
  if (hasOffscreenCanvas) {
    return new OffscreenCanvas(256, 256)
  } else {
    const canvas = document.createElement('canvas')
    canvas.width = 256
    canvas.height = 256
    return canvas
  }
}
const canvas = createCanvas()

const ctx = canvas.getContext('2d')

async function createNoCacheCanvas(x, y, z) {
  if (!ctx) {
    return null
  }

  // 清空并绘制背景
  ctx.clearRect(0, 0, 256, 256)
  ctx.fillStyle = 'rgba(220, 220, 220, 0.7)'
  ctx.fillRect(0, 0, 256, 256)

  ctx.fillStyle = '#708090'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(`x: ${x}, y: ${y}, z: ${z}`, 128, 100)

  // 添加文本
  ctx.fillStyle = '#708090'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText('N/A', 128, 140)

  // 绘制边框
  ctx.strokeStyle = '#708090'
  ctx.lineWidth = 1
  ctx.strokeRect(0, 0, 256, 256)

  try {
    if (hasOffscreenCanvas && canvas instanceof OffscreenCanvas) {
      const blob = await canvas.convertToBlob()
      const buffer = await blob.arrayBuffer()
      return new Uint8Array(buffer)
    } else if (canvas instanceof HTMLCanvasElement) {
      const blob = await new Promise((resolve, reject) => {
        canvas.toBlob(blob => {
          if (blob) resolve(blob)
          else reject(new Error('HTMLCanvasElement.toBlob 失败'))
        })
      })
      const buffer = await blob.arrayBuffer()
      return new Uint8Array(buffer)
    } else {
      return null
    }
  } catch (err) {
    bfglob.console.error('createNoCacheCanvas: ', err)
    return null
  }
}

let { protocol, hostname, port } = window.location

if (port === '') {
  port = protocol === 'https' ? '443' : '80'
}

protocol = protocol.endsWith(':') ? protocol.slice(0, -1) : protocol

export const rpcUrl = `${protocol}://${hostname}:${port}`

export const MapLibreProtoColName = 'getMapTile'

// 拦截地图瓦片的请求
// 注册自定义协议处理器，处理图片请求失败的情况
export function registerCustomProtocol() {
  maplibregl.addProtocol(MapLibreProtoColName, (params, abortController) => {
    return new Promise(resolve => {
      const url = params.url.replace(`${MapLibreProtoColName}://`, '')
      fetch(url, { signal: abortController.signal })
        .then(response => {
          if (!response.ok) {
            throw new Error(`请求失败: ${response.status}`)
          }
          return response.blob()
        })
        .then(res => {
          // 直接返回原始数据
          res.arrayBuffer().then(buffer => {
            resolve({ data: new Uint8Array(buffer) })
          })
        })
        .catch(() => {
          // 请求失败时创建错误占位图
          const params = new URLSearchParams(new URL(url).search)
          createNoCacheCanvas(params.get('x'), params.get('y'), params.get('z')).then(noCacheData => {
            resolve({
              data: noCacheData ?? new Uint8Array(0),
            })
          })
        })
    })
  })
}

// 设备操作函数映射
const deviceActions = {
  quickCall: deviceRid => {
    let dmrId = ''
    // 首先尝试从设备数据中获取
    const deviceData = bfglob.gdevices.get(deviceRid)
    if (deviceData?.dmrId) {
      dmrId = deviceData.dmrId
    } else {
      // 如果设备数据中没有，尝试从组织数据中获取
      const orgData = bfglob.gorgData.get(deviceRid)
      dmrId = orgData?.dmrId ?? ''
    }

    if (dmrId) {
      // 检查是否已经打开了 BfSpeaking 对话框
      if (bfglob.vspeaking && bfglob.vspeaking.visible) {
        // 如果已经打开，直接设置目标并快速呼叫
        setSpeakTarget(deviceRid)
        bfglob.vspeaking.speakFast(dmrId)
      } else {
        // 如果没有打开，则打开对话框
        import('@/platform/dispatch/CommunicationDispatch/dialog/bfSpeaking.vue').then(BfSpeaking => {
          openDialog(BfSpeaking.default).then(vm => {
            setSpeakTarget(deviceRid)
            vm.value.speakFast(dmrId)
          })
        })
      }
    } else {
      console.warn('无法获取设备的 dmrId:', deviceRid)
    }
  },

  telecontrol: deviceRid => {
    console.log('遥开遥毙:', deviceRid)
    // 打开遥开遥毙对话框
    import('@/platform/dispatch/gisApplication/mapQuickSendCmd.vue').then(DeviceControlComponent => {
      openDialog(DeviceControlComponent.default, { rid: deviceRid, cmdType: 'cb09' })
    })
  },

  locationMonitor: deviceRid => {
    console.log('定位监控:', deviceRid)
    // 打开定位监控对话框
    import('@/platform/dispatch/gisApplication/mapQuickSendCmd.vue').then(DeviceControlComponent => {
      openDialog(DeviceControlComponent.default, { rid: deviceRid, cmdType: 'cb01' })
    })
  },

  trackMonitor: deviceRid => {
    console.log('跟踪监控:', deviceRid)
    // 打开跟踪监控对话框
    import('@/platform/dispatch/gisApplication/mapQuickSendCmd.vue').then(DeviceControlComponent => {
      openDialog(DeviceControlComponent.default, { rid: deviceRid, cmdType: 'cb02' })
    })
  },

  statusCheck: deviceRid => {
    // 通过 deviceRid 获取设备数据
    const device = bfglob.gdevices.get(deviceRid)
    if (!device) {
      bfglob.console.error('未找到设备:', deviceRid)
      return
    }

    // 触发创建设备状态表事件
    bfglob.emit('create_device_status_table', device)
  },
}

// 使用事件委托处理设备操作按钮点击
function initDeviceActionEventDelegation() {
  // 移除之前的事件监听器（如果存在）
  document.removeEventListener('click', handleDeviceActionClick)

  // 添加事件委托
  document.addEventListener('click', handleDeviceActionClick)
}

function handleDeviceActionClick(event) {
  const button = event.target.closest('.device-action-btn')
  if (!button) return

  const action = button.getAttribute('data-action')
  if (!action) return

  const actionContainer = button.closest('.popup-device-actions')
  if (!actionContainer) return

  const deviceRid = actionContainer.getAttribute('data-device-rid')
  if (!deviceRid) return

  // 执行对应的操作
  const actionFunction = deviceActions[action]
  if (actionFunction) {
    actionFunction(deviceRid)
  }
}

// 初始化事件委托
initDeviceActionEventDelegation()
